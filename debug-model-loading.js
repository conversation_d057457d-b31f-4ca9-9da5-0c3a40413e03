const { app, BrowserWindow } = require('electron');
const path = require('path');

// Import the IPC handlers
require('./src/ipcHandlers');

let mainWindow;

function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'src', 'preload.js')
        },
        show: true
    });
    mainWindow.loadFile('src/renderer/index.html');
    
    // Open DevTools to see the console
    mainWindow.webContents.openDevTools();
}

async function debugModelLoading() {
    console.log('\n🔧 DEBUG MODEL LOADING');
    console.log('🎯 Checking why models are not loading in dropdown');
    console.log('🔍 Look at the browser console for detailed logs\n');
    
    // Wait for the window to load
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    try {
        // Test the IPC handler directly
        console.log('🧪 Testing IPC handler directly...');
        
        const { ipcMain } = require('electron');
        
        // Simulate the IPC call
        const testResult = await new Promise((resolve) => {
            const mockEvent = { reply: () => {} };
            
            // Find the get-all-models handler
            const handlers = ipcMain.eventNames();
            console.log('📋 Available IPC handlers:', handlers);
            
            // Call the handler directly
            ipcMain.emit('get-all-models', mockEvent);
            
            setTimeout(() => {
                resolve({ tested: true });
            }, 1000);
        });
        
        console.log('📊 IPC test result:', testResult);
        
        // Execute JavaScript in the renderer to test manually
        const result = await mainWindow.webContents.executeJavaScript(`
            (async function() {
                console.log('🔧 Manual model loading test...');
                
                // Check if electronAPI is available
                console.log('📡 electronAPI available:', !!window.electronAPI);
                console.log('📡 getAllModels available:', !!window.electronAPI?.getAllModels);
                
                if (window.electronAPI && window.electronAPI.getAllModels) {
                    try {
                        console.log('📞 Calling getAllModels...');
                        const result = await window.electronAPI.getAllModels();
                        console.log('📊 getAllModels result:', result);
                        return result;
                    } catch (error) {
                        console.error('❌ Error calling getAllModels:', error);
                        return { error: error.message };
                    }
                } else {
                    console.error('❌ electronAPI.getAllModels not available');
                    return { error: 'API not available' };
                }
            })();
        `);
        
        console.log('\n📊 MANUAL TEST RESULT:');
        console.log('═'.repeat(50));
        console.log(JSON.stringify(result, null, 2));
        
        if (result.success && result.models) {
            console.log('\n✅ IPC HANDLER IS WORKING!');
            console.log(`📋 Found ${result.models.length} models:`);
            result.models.forEach((model, index) => {
                console.log(`   ${index + 1}. ${model.id} (${model.name})`);
            });
            
            console.log('\n🔧 The issue might be in the frontend initialization');
            console.log('🔍 Check the browser console for frontend errors');
        } else {
            console.log('\n❌ IPC HANDLER IS NOT WORKING');
            console.log('🔧 Error:', result.error);
        }
        
    } catch (error) {
        console.error('\n❌ Debug test failed:', error.message);
        console.error('Stack:', error.stack);
    }
    
    console.log('\n🔍 The application window is open for manual inspection.');
    console.log('📖 Check the browser DevTools console for detailed logs.');
    console.log('Press Ctrl+C to exit when done.\n');
}

app.whenReady().then(() => {
    createWindow();
    setTimeout(debugModelLoading, 3000);
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

process.on('SIGINT', () => {
    console.log('\n🛑 Debug interrupted by user');
    app.quit();
});
