#!/usr/bin/env node

/**
 * Desktop Application Startup Script
 * This script starts the MCQ & TF Question Generator desktop application
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 Starting MCQ & TF Question Generator Desktop Application...\n');

// Check if required files exist
const requiredFiles = [
    'src/main.js',
    'src/preload.js',
    'src/renderer/index.html',
    'package.json'
];

console.log('📋 Checking required files...');
for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
        console.error(`❌ Required file missing: ${file}`);
        process.exit(1);
    }
    console.log(`✅ ${file}`);
}

// Check if node_modules exists
if (!fs.existsSync('node_modules')) {
    console.log('\n📦 Installing dependencies...');
    const npmInstall = spawn('npm', ['install'], { stdio: 'inherit' });
    
    npmInstall.on('close', (code) => {
        if (code !== 0) {
            console.error('❌ Failed to install dependencies');
            process.exit(1);
        }
        startApplication();
    });
} else {
    startApplication();
}

function startApplication() {
    console.log('\n🎯 Launching application...');

    // Set environment variable for development
    process.env.NODE_ENV = process.env.NODE_ENV || 'development';

    // Try different ways to start Electron
    const electronPaths = [
        path.join(__dirname, 'node_modules', '.bin', 'electron'),
        path.join(__dirname, 'node_modules', '.bin', 'electron.cmd'),
        'electron'
    ];

    let electronPath = null;
    for (const ePath of electronPaths) {
        if (fs.existsSync(ePath)) {
            electronPath = ePath;
            break;
        }
    }

    if (!electronPath) {
        // Try using npm run start instead
        console.log('🔄 Using npm start...');
        const npmStart = spawn('npm', ['start'], {
            stdio: 'inherit',
            env: { ...process.env },
            shell: true
        });

        npmStart.on('close', (code) => {
            console.log(`\n📱 Application closed with code: ${code}`);
            process.exit(code);
        });

        npmStart.on('error', (error) => {
            console.error('❌ Failed to start application:', error);
            console.log('\n💡 Try running: npm install electron --save-dev');
            process.exit(1);
        });
        return;
    }

    // Start Electron directly
    const electron = spawn(electronPath, ['.'], {
        stdio: 'inherit',
        env: { ...process.env },
        shell: process.platform === 'win32'
    });

    electron.on('close', (code) => {
        console.log(`\n📱 Application closed with code: ${code}`);
        process.exit(code);
    });

    electron.on('error', (error) => {
        console.error('❌ Failed to start application:', error);
        console.log('\n💡 Try running: npm install electron --save-dev');
        process.exit(1);
    });
}

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n👋 Shutting down gracefully...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n👋 Shutting down gracefully...');
    process.exit(0);
});
