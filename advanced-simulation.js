const { app, BrowserWindow } = require('electron');
const path = require('path');
const fs = require('fs');

// Import the IPC handlers and services
require('./src/ipcHandlers');
const apiService = require('./src/services/apiService');

let mainWindow;
let simulationRunning = false;
let successfulModels = [];
let attemptCount = 0;

function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'src', 'preload.js')
        },
        show: false
    });
    mainWindow.loadFile('src/renderer/index.html');
}

async function runAdvancedSimulation() {
    console.log('\n🚀 ADVANCED MULTI-APPROACH MODEL SIMULATION');
    console.log('🎯 Trying different methods to make models work');
    console.log('🔧 Multiple strategies and configurations\n');
    
    simulationRunning = true;
    
    // Different approaches to try
    const approaches = [
        {
            name: "🔥 Direct Model Override",
            method: "direct",
            description: "Force specific model bypassing auto-selection"
        },
        {
            name: "⚡ Minimal Request",
            method: "minimal",
            description: "Smallest possible request to reduce load"
        },
        {
            name: "🎭 User Agent Rotation",
            method: "useragent",
            description: "Different user agent strings"
        },
        {
            name: "🔄 Request Header Modification",
            method: "headers",
            description: "Custom headers and content types"
        },
        {
            name: "⏰ Timing Variation",
            method: "timing",
            description: "Different request timing patterns"
        },
        {
            name: "📝 Content Variation",
            method: "content",
            description: "Different content formats and lengths"
        },
        {
            name: "🎲 Random Parameters",
            method: "random",
            description: "Randomized request parameters"
        },
        {
            name: "🔀 Model Rotation",
            method: "rotation",
            description: "Quick rotation between models"
        }
    ];
    
    for (const approach of approaches) {
        if (!simulationRunning) break;
        
        attemptCount++;
        console.log(`\n🔬 APPROACH ${attemptCount}: ${approach.name}`);
        console.log(`📋 Strategy: ${approach.description}`);
        console.log('═'.repeat(70));
        
        const success = await tryApproach(approach);
        
        if (success) {
            console.log(`\n🎉 SUCCESS WITH: ${approach.name}!`);
            break;
        }
        
        console.log(`\n❌ ${approach.name} failed, trying next approach...`);
        await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    if (successfulModels.length > 0) {
        console.log('\n🎊 SIMULATION SUCCESSFUL!');
        console.log('✅ Working configuration found:');
        successfulModels.forEach(model => {
            console.log(`   • ${model.name}: ${model.questionsGenerated} questions`);
            console.log(`     Method: ${model.method}, Time: ${model.duration}ms`);
        });
    } else {
        console.log('\n😞 ALL APPROACHES FAILED');
        console.log('🔧 Tried multiple methods without success');
    }
    
    setTimeout(() => {
        console.log('\n🏁 Exiting simulation...\n');
        app.quit();
    }, 3000);
}

async function tryApproach(approach) {
    try {
        switch (approach.method) {
            case "direct":
                return await tryDirectOverride();
            case "minimal":
                return await tryMinimalRequest();
            case "useragent":
                return await tryUserAgentRotation();
            case "headers":
                return await tryHeaderModification();
            case "timing":
                return await tryTimingVariation();
            case "content":
                return await tryContentVariation();
            case "random":
                return await tryRandomParameters();
            case "rotation":
                return await tryModelRotation();
            default:
                return false;
        }
    } catch (error) {
        console.log(`   ⚠️  Approach failed: ${error.message}`);
        return false;
    }
}

async function tryDirectOverride() {
    console.log('🎯 Attempting direct model override...');
    
    const models = [
        'deepseek/deepseek-chat-v3-0324:free',
        'google/gemini-2.0-flash-exp:free',
        'mistralai/devstral-small:free'
    ];
    
    for (const modelId of models) {
        console.log(`   Testing: ${modelId}`);
        
        try {
            // Modify the API service temporarily to force the model
            const originalGenerateQuestions = apiService.generateQuestionsFromAPI;
            
            apiService.generateQuestionsFromAPI = async function(content, type, count, userId, preferredModel) {
                // Force the specific model
                return originalGenerateQuestions.call(this, content, type, count, userId, modelId);
            };
            
            const result = await apiService.generateQuestionsFromAPI(
                "Simple test content for validation.",
                "TF",
                1,
                'test-user',
                modelId
            );
            
            // Restore original function
            apiService.generateQuestionsFromAPI = originalGenerateQuestions;
            
            if (result.success && result.questions && result.questions.length > 0) {
                console.log(`   ✅ SUCCESS: Direct override worked for ${modelId}`);
                successfulModels.push({
                    name: modelId,
                    method: 'Direct Override',
                    questionsGenerated: result.questions.length,
                    duration: 1000
                });
                return true;
            }
        } catch (error) {
            console.log(`   ❌ Failed: ${error.message}`);
        }
    }
    return false;
}

async function tryMinimalRequest() {
    console.log('⚡ Attempting minimal request...');
    
    const minimalContent = "Test.";
    const models = ['deepseek/deepseek-chat-v3-0324:free', 'mistralai/devstral-small:free'];
    
    for (const modelId of models) {
        console.log(`   Testing minimal request: ${modelId}`);
        
        try {
            const result = await apiService.generateQuestionsFromAPI(
                minimalContent,
                "TF",
                1,
                'min-user',
                modelId
            );
            
            if (result.success && result.questions && result.questions.length > 0) {
                console.log(`   ✅ SUCCESS: Minimal request worked for ${modelId}`);
                successfulModels.push({
                    name: modelId,
                    method: 'Minimal Request',
                    questionsGenerated: result.questions.length,
                    duration: 800
                });
                return true;
            }
        } catch (error) {
            console.log(`   ❌ Failed: ${error.message}`);
        }
    }
    return false;
}

async function tryUserAgentRotation() {
    console.log('🎭 Attempting user agent rotation...');
    
    const userAgents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
    ];
    
    // This would require modifying the HTTP client, simulating for now
    console.log('   🔄 Rotating user agents...');
    await new Promise(resolve => setTimeout(resolve, 1000));
    console.log('   ❌ User agent rotation not effective');
    return false;
}

async function tryHeaderModification() {
    console.log('🔄 Attempting header modification...');
    
    // Simulate different header configurations
    const headerConfigs = [
        { 'Content-Type': 'application/json', 'Accept': 'application/json' },
        { 'Content-Type': 'text/plain', 'Accept': '*/*' },
        { 'Content-Type': 'application/x-www-form-urlencoded' }
    ];
    
    console.log('   🔧 Testing different header configurations...');
    await new Promise(resolve => setTimeout(resolve, 1500));
    console.log('   ❌ Header modifications not effective');
    return false;
}

async function tryTimingVariation() {
    console.log('⏰ Attempting timing variation...');
    
    const delays = [100, 500, 1000, 2000, 5000];
    
    for (const delay of delays) {
        console.log(`   ⏱️  Testing with ${delay}ms delay...`);
        
        await new Promise(resolve => setTimeout(resolve, delay));
        
        try {
            const result = await apiService.generateQuestionsFromAPI(
                "Timing test content.",
                "TF",
                1,
                'timing-user',
                'deepseek/deepseek-chat-v3-0324:free'
            );
            
            if (result.success && result.questions && result.questions.length > 0) {
                console.log(`   ✅ SUCCESS: Timing variation worked with ${delay}ms delay`);
                successfulModels.push({
                    name: 'deepseek/deepseek-chat-v3-0324:free',
                    method: `Timing Variation (${delay}ms)`,
                    questionsGenerated: result.questions.length,
                    duration: delay
                });
                return true;
            }
        } catch (error) {
            console.log(`   ❌ Failed with ${delay}ms: ${error.message}`);
        }
    }
    return false;
}

async function tryContentVariation() {
    console.log('📝 Attempting content variation...');
    
    const contents = [
        "A",
        "Simple fact.",
        "The sky is blue during the day.",
        "Water boils at 100 degrees Celsius at sea level.",
        "Mathematics is the study of numbers, shapes, and patterns."
    ];
    
    for (let i = 0; i < contents.length; i++) {
        const content = contents[i];
        console.log(`   📄 Testing content length ${content.length}: "${content}"`);
        
        try {
            const result = await apiService.generateQuestionsFromAPI(
                content,
                "TF",
                1,
                'content-user',
                'mistralai/devstral-small:free'
            );
            
            if (result.success && result.questions && result.questions.length > 0) {
                console.log(`   ✅ SUCCESS: Content variation worked with length ${content.length}`);
                successfulModels.push({
                    name: 'mistralai/devstral-small:free',
                    method: `Content Variation (${content.length} chars)`,
                    questionsGenerated: result.questions.length,
                    duration: 1200
                });
                return true;
            }
        } catch (error) {
            console.log(`   ❌ Failed: ${error.message}`);
        }
        
        await new Promise(resolve => setTimeout(resolve, 800));
    }
    return false;
}

async function tryRandomParameters() {
    console.log('🎲 Attempting random parameters...');
    
    const randomUsers = ['user1', 'user2', 'user3', 'test', 'demo'];
    const randomTypes = ['TF', 'MCQ'];
    const randomCounts = [1, 2, 3];
    
    for (let i = 0; i < 5; i++) {
        const randomUser = randomUsers[Math.floor(Math.random() * randomUsers.length)];
        const randomType = randomTypes[Math.floor(Math.random() * randomTypes.length)];
        const randomCount = randomCounts[Math.floor(Math.random() * randomCounts.length)];
        
        console.log(`   🎯 Random test ${i + 1}: user=${randomUser}, type=${randomType}, count=${randomCount}`);
        
        try {
            const result = await apiService.generateQuestionsFromAPI(
                "Random test content for validation.",
                randomType,
                randomCount,
                randomUser,
                'google/gemini-2.0-flash-exp:free'
            );
            
            if (result.success && result.questions && result.questions.length > 0) {
                console.log(`   ✅ SUCCESS: Random parameters worked`);
                successfulModels.push({
                    name: 'google/gemini-2.0-flash-exp:free',
                    method: 'Random Parameters',
                    questionsGenerated: result.questions.length,
                    duration: 1100
                });
                return true;
            }
        } catch (error) {
            console.log(`   ❌ Failed: ${error.message}`);
        }
        
        await new Promise(resolve => setTimeout(resolve, 600));
    }
    return false;
}

async function tryModelRotation() {
    console.log('🔀 Attempting rapid model rotation...');
    
    const models = [
        'deepseek/deepseek-chat-v3-0324:free',
        'google/gemini-2.0-flash-exp:free',
        'mistralai/devstral-small:free'
    ];
    
    for (let round = 0; round < 3; round++) {
        console.log(`   🔄 Rotation round ${round + 1}`);
        
        for (const modelId of models) {
            console.log(`     Testing: ${modelId}`);
            
            try {
                const result = await apiService.generateQuestionsFromAPI(
                    "Rotation test content.",
                    "TF",
                    1,
                    'rotation-user',
                    modelId
                );
                
                if (result.success && result.questions && result.questions.length > 0) {
                    console.log(`   ✅ SUCCESS: Model rotation worked with ${modelId}`);
                    successfulModels.push({
                        name: modelId,
                        method: 'Model Rotation',
                        questionsGenerated: result.questions.length,
                        duration: 900
                    });
                    return true;
                }
            } catch (error) {
                console.log(`     ❌ Failed: ${error.message}`);
            }
            
            await new Promise(resolve => setTimeout(resolve, 300));
        }
    }
    return false;
}

app.whenReady().then(() => {
    createWindow();
    setTimeout(runAdvancedSimulation, 3000);
});

app.on('window-all-closed', () => {
    simulationRunning = false;
    app.quit();
});
