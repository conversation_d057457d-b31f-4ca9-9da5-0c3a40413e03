/* CSS Variables for Theme System */
:root {
    /* Light Theme Colors */
    --bg-primary: #f5f7fa;
    --bg-secondary: #ffffff;
    --bg-tertiary: #f8f9fa;
    --bg-accent: #f0f4ff;
    --bg-hover: #f8f9ff;

    --text-primary: #333333;
    --text-secondary: #666666;
    --text-muted: #999999;
    --text-inverse: #ffffff;

    --border-color: #e9ecef;
    --border-hover: #667eea;
    --border-focus: #667eea;

    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);

    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 20px rgba(0,0,0,0.1);
    --shadow-heavy: 0 8px 25px rgba(102, 126, 234, 0.15);

    --success-color: #28a745;
    --error-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --hover-transform: translateY(-1px);

    --success-bg: #d1f2d1;
    --error-bg: #f8d7da;
    --warning-bg: #fff3cd;
    --info-bg: #d1ecf1;
}

/* Dark Theme Colors */
[data-theme="dark"] {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #3a3a3a;
    --bg-accent: #2a2a3a;
    --bg-hover: #3a3a4a;

    --text-primary: #e0e0e0;
    --text-secondary: #b0b0b0;
    --text-muted: #808080;
    --text-inverse: #1a1a1a;

    --border-color: #404040;
    --border-hover: #667eea;
    --border-focus: #667eea;

    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);

    --shadow-light: 0 2px 10px rgba(0,0,0,0.3);
    --shadow-medium: 0 4px 20px rgba(0,0,0,0.3);
    --shadow-heavy: 0 8px 25px rgba(102, 126, 234, 0.25);

    --success-color: #4caf50;
    --error-color: #f44336;
    --warning-color: #ff9800;
    --info-color: #2196f3;

    --success-bg: #1b4332;
    --error-bg: #5c1a1a;
    --warning-bg: #5c3317;
    --info-bg: #1a365d;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    overflow: hidden;
    transition: background-color 0.3s ease, color 0.3s ease;
}

#app {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.app-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo i {
    font-size: 1.5rem;
}

.logo h1 {
    font-size: 1.2rem;
    font-weight: 300;
}

.header-actions {
    display: flex;
    gap: 0.5rem;
}

/* Button Styles */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-icon {
    padding: 0.5rem;
    background: rgba(255,255,255,0.1);
    color: white;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Main Content */
.main-content {
    flex: 1;
    overflow: hidden;
    position: relative;
}

/* Screen Management */
.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow-y: auto;
}

.screen.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Welcome Screen */
.welcome-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    text-align: center;
}

.welcome-header {
    margin-bottom: 3rem;
}

.welcome-icon {
    font-size: 4rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.welcome-header h2 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.welcome-header p {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.question-type-selection {
    margin-bottom: 3rem;
}

.question-type-selection h3 {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.type-buttons {
    display: flex;
    gap: 2rem;
    justify-content: center;
    flex-wrap: wrap;
}

.type-btn {
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 2rem;
    width: 250px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-primary);
}

.type-btn:hover {
    border-color: var(--border-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
}

.type-btn.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.type-btn i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.type-btn span {
    font-size: 1.1rem;
    font-weight: 600;
}

.type-btn small {
    opacity: 0.8;
    font-size: 0.9rem;
}

.input-methods h3 {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.input-options {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.input-btn {
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    width: 150px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.input-btn:hover {
    border-color: var(--border-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
}

.input-btn i {
    font-size: 1.5rem;
    color: #667eea;
}

.input-btn span {
    font-weight: 500;
    color: var(--text-primary);
}

/* Question Settings */
.question-settings {
    margin-top: 2rem;
    padding: 1.5rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
}

.question-settings h3 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.setting-item:hover {
    border-color: var(--border-hover);
    box-shadow: var(--shadow-light);
}

.setting-info {
    flex: 1;
}

.setting-info label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    cursor: pointer;
}

.setting-info label i {
    color: #667eea;
    font-size: 1.1rem;
}

.setting-description {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.3;
}

.setting-control {
    margin-left: 1rem;
}

.setting-input {
    width: 80px;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 1rem;
    text-align: center;
    transition: all 0.3s ease;
}

.setting-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.setting-input:hover {
    border-color: var(--border-hover);
}

/* Content Screen */
.content-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 2rem;
}

.screen-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.screen-header h2 {
    flex: 1;
    color: var(--text-primary);
}

.header-nav {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.quiz-nav {
    display: flex;
    align-items: center;
}

.input-area {
    display: none;
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow-medium);
}

.input-area.active {
    display: block;
}

/* Text Input */
#textContent {
    width: 100%;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
    min-height: 200px;
    transition: border-color 0.3s ease;
    background: var(--bg-secondary);
    color: var(--text-primary);
}

#textContent:focus {
    outline: none;
    border-color: var(--border-focus);
}

/* Upload Zones */
.upload-zone {
    border: 2px dashed var(--border-color);
    border-radius: 12px;
    padding: 3rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: var(--bg-tertiary);
}

.upload-zone:hover {
    border-color: var(--border-hover);
    background: var(--bg-hover);
}

.upload-zone.dragover {
    border-color: var(--border-hover);
    background: var(--bg-accent);
    transform: scale(1.02);
}

.upload-zone i {
    font-size: 3rem;
    color: var(--text-muted);
    margin-bottom: 1rem;
    display: block;
}

.upload-zone p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.upload-zone small {
    color: var(--text-muted);
}

/* File Info */
.file-info {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.file-details {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.file-details i {
    color: #667eea;
}

/* Image Preview */
.image-preview {
    position: relative;
    display: inline-block;
    margin-bottom: 1rem;
}

.image-preview img {
    max-width: 300px;
    max-height: 200px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.image-preview .btn {
    position: absolute;
    top: -10px;
    right: -10px;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Input Actions */
.input-actions {
    margin-top: 2rem;
    text-align: center;
}

/* Processing Screen */
.processing-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    height: auto;
    text-align: center;
    max-width: 350px;
    margin: 20vh auto;
    padding: 1.5rem;
    background: var(--bg-secondary);
    border-radius: 12px;
    box-shadow: var(--shadow-medium);
}

.processing-animation {
    font-size: 2.5rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.processing-container h2 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    font-size: 1.5rem;
}

.processing-container p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

#processingScreen .progress-bar {
    width: 100%;
    max-width: 300px;
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.2);
    border: 1px solid #ddd;
}

[data-theme="dark"] #processingScreen .progress-bar {
    background-color: #404040;
    border-color: #555;
}

#processingScreen .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.5s ease-out;
    border-radius: 4px;
    min-width: 2px;
}

/* Test style to make progress visible */
#processingScreen .progress-fill[style*="width"] {
    min-width: 4px;
}

.processing-container .progress-text {
    color: #667eea;
    font-weight: 600;
    font-size: 0.8rem;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
}

.loading-spinner i {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 1rem;
}

/* Notification Container */
.notification-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1001;
    max-width: 400px;
}

.notification {
    background: white;
    border-left: 4px solid #667eea;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    max-width: 350px;
    animation: slideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    word-wrap: break-word;
}

.notification.success {
    border-left-color: #28a745;
}

.notification.error {
    border-left-color: #dc3545;
}

.notification.warning {
    border-left-color: #ffc107;
}

@keyframes slideIn {
    from {
        transform: translateY(100%) translateX(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0) translateX(0);
        opacity: 1;
    }
}

/* Questions Display */
.questions-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem;
}

.question-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.question-actions .btn {
    min-width: 180px;
}

.questions-display {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow-medium);
}

.question-item {
    border-bottom: 1px solid var(--border-color);
    padding: 1.5rem 0;
}

.question-item:last-child {
    border-bottom: none;
}

.question-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.question-number {
    background: #667eea;
    color: white;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
}

.question-text {
    flex: 1;
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--text-primary);
}

.question-options {
    margin-left: 3rem;
    margin-bottom: 1rem;
}

.option-item {
    padding: 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.option-letter {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.8rem;
    color: var(--text-primary);
}

.option-item.correct .option-letter {
    background: #d4edda;
    border-color: #28a745;
    color: #28a745;
}

.question-answer {
    margin-left: 3rem;
    padding: 1rem;
    background: var(--success-bg);
    border-radius: 8px;
    border-left: 4px solid var(--success-color);
    color: var(--text-primary);
}

.answer-label {
    font-weight: 600;
    color: #28a745;
    margin-bottom: 0.5rem;
}

.question-explanation {
    margin-left: 3rem;
    margin-top: 1rem;
    padding: 1rem;
    background: var(--warning-bg);
    border-radius: 8px;
    border-left: 4px solid var(--warning-color);
    color: var(--text-primary);
}

.explanation-label {
    font-weight: 600;
    color: #856404;
    margin-bottom: 0.5rem;
}

/* Quiz Screen */
.quiz-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.quiz-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: var(--bg-secondary);
    border-radius: 12px;
    box-shadow: var(--shadow-medium);
    margin-bottom: 2rem;
    gap: 1rem;
}

.quiz-progress {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.quiz-score {
    font-size: 1.1rem;
    color: #667eea;
}

.quiz-content {
    flex: 1;
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow-medium);
    display: flex;
    flex-direction: column;
}

.quiz-question {
    font-size: 1.2rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.quiz-options {
    flex: 1;
    margin-bottom: 2rem;
}

.quiz-option {
    background: var(--bg-tertiary);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
    color: var(--text-primary);
}

.quiz-option:hover {
    border-color: var(--border-hover);
    background: var(--bg-secondary);
    transform: var(--hover-transform);
}

.quiz-option.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.quiz-option.correct {
    border-color: var(--success-color);
    background: var(--success-bg);
    color: var(--text-primary);
}

.quiz-option.incorrect {
    border-color: var(--error-color);
    background: var(--error-bg);
    color: var(--text-primary);
}

.option-indicator {
    background: var(--bg-secondary);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.quiz-option.selected .option-indicator {
    background: white;
    color: #667eea;
    border: 1px solid #667eea;
}

.quiz-feedback {
    background: var(--bg-tertiary);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    color: var(--text-primary);
}

.feedback-correct {
    border-left: 4px solid var(--success-color);
    background: var(--success-bg);
    color: var(--text-primary);
}

.feedback-incorrect {
    border-left: 4px solid var(--error-color);
    background: var(--error-bg);
    color: var(--text-primary);
}

.feedback-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.feedback-correct .feedback-title {
    color: var(--text-primary);
}

.feedback-incorrect .feedback-title {
    color: var(--text-primary);
}

/* Ensure all feedback text is visible */
.quiz-feedback p,
.quiz-feedback span,
.quiz-feedback div {
    color: var(--text-primary) !important;
}

.feedback-correct p,
.feedback-correct span,
.feedback-correct div {
    color: var(--text-primary) !important;
}

.feedback-incorrect p,
.feedback-incorrect span,
.feedback-incorrect div {
    color: var(--text-primary) !important;
}

.quiz-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Results Screen */
.results-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    text-align: center;
}

.results-header {
    margin-bottom: 3rem;
}

.results-icon {
    font-size: 4rem;
    color: #ffc107;
    margin-bottom: 1rem;
}

.results-display {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow-medium);
    margin-bottom: 2rem;
}

.score-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.score-item {
    background: var(--bg-tertiary);
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
}

.score-value {
    font-size: 2rem;
    font-weight: 600;
    color: #667eea;
    display: block;
}

.score-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

.results-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* History Screen */
.history-content {
    padding: 2rem 0;
}

.history-filters {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    flex-wrap: wrap;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 150px;
}

.filter-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.filter-select {
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.filter-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.history-stats-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.history-list {
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.history-item {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s ease;
    cursor: pointer;
}

.history-item:hover {
    background: var(--bg-primary);
    transform: translateX(5px);
}

.history-item:last-child {
    border-bottom: none;
}

.history-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.history-item-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
    color: var(--text-primary);
}

.history-item-title i {
    color: #667eea;
    font-size: 1.2rem;
}

.history-item-date {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.history-item-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.history-stat {
    text-align: center;
    padding: 0.75rem;
    background: var(--bg-primary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.history-stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.history-stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.score-excellent { color: #10b981; }
.score-good { color: #3b82f6; }
.score-average { color: #f59e0b; }
.score-poor { color: #ef4444; }

/* Statistics Screen */
.statistics-content {
    padding: 2rem 0;
}

.stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.stats-grid {
    display: grid;
    gap: 2rem;
    margin-bottom: 3rem;
}

.stats-section {
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    padding: 2rem;
}

.stats-section h3 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0 0 1.5rem 0;
    color: var(--text-primary);
    font-size: 1.3rem;
}

.stats-section h3 i {
    color: #667eea;
}

.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
    border-color: #667eea;
}

.stat-card.large {
    padding: 2rem;
    min-height: 140px;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.stat-card.large .stat-icon {
    width: 80px;
    height: 80px;
}

.stat-icon i {
    color: white;
    font-size: 1.5rem;
}

.stat-card.large .stat-icon i {
    font-size: 2rem;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    line-height: 1;
}

.stat-card.large .stat-number {
    font-size: 3rem;
}

.stat-label {
    color: var(--text-secondary);
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-sublabel {
    color: var(--text-secondary);
    font-size: 0.8rem;
    margin-top: 0.25rem;
    opacity: 0.8;
}

.stat-progress {
    margin-top: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 4px;
    transition: width 0.8s ease;
    width: 0%;
}

.progress-text {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--text-primary);
    min-width: 35px;
}

/* Achievements */
.achievements-section {
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    padding: 2rem;
}

.achievements-section h3 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0 0 1.5rem 0;
    color: var(--text-primary);
    font-size: 1.3rem;
}

.achievements-section h3 i {
    color: #f59e0b;
}

.achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.achievement-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.achievement-card.unlocked {
    border-color: #f59e0b;
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(245, 158, 11, 0.05));
}

.achievement-card.unlocked::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #f59e0b, #d97706);
}

.achievement-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    transition: all 0.3s ease;
}

.achievement-card.unlocked .achievement-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    transform: scale(1.1);
}

.achievement-icon i {
    font-size: 1.5rem;
    color: var(--text-secondary);
}

.achievement-card.unlocked .achievement-icon i {
    color: white;
}

.achievement-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.achievement-description {
    font-size: 0.9rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

.achievement-card.unlocked .achievement-title {
    color: #f59e0b;
}

/* Loading States */
.loading-placeholder {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
}

.loading-placeholder i {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #667eea;
}

.loading-placeholder p {
    margin: 0;
    font-size: 1.1rem;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--border-color);
}

.empty-state h3 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
}

.empty-state p {
    margin: 0;
    line-height: 1.5;
}

/* Floating Back Button */
.floating-back-btn {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    transform: scale(0);
    opacity: 0;
}

.floating-back-btn[style*="flex"] {
    transform: scale(1);
    opacity: 1;
    animation: bounceIn 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.floating-back-btn:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.floating-back-btn:active {
    transform: translateY(-1px) scale(1.05);
}

@keyframes bounceIn {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .type-buttons {
        flex-direction: column;
        align-items: center;
    }

    .input-options {
        flex-direction: column;
        align-items: center;
    }

    .quiz-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .question-actions {
        flex-direction: column;
    }

    .results-actions {
        flex-direction: column;
        align-items: center;
    }

    /* History & Statistics Mobile */
    .history-filters {
        flex-direction: column;
        gap: 1rem;
    }

    .filter-group {
        min-width: auto;
    }

    .history-stats-summary {
        grid-template-columns: 1fr;
    }

    .history-item-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .stats-overview {
        grid-template-columns: 1fr;
    }

    .stats-cards {
        grid-template-columns: 1fr;
    }

    .achievements-grid {
        grid-template-columns: 1fr;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .stat-card.large {
        min-height: auto;
    }

    .stat-progress {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    /* Floating button mobile adjustments */
    .floating-back-btn {
        bottom: 20px;
        left: 20px;
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    /* Notification container mobile adjustments */
    .notification-container {
        bottom: 10px;
        right: 10px;
        max-width: calc(100vw - 100px);
    }

    .notification {
        max-width: 100%;
        font-size: 0.9rem;
    }

    /* Processing screen mobile adjustments */
    .processing-container {
        padding: 1rem;
        max-width: 95%;
        margin: 15vh auto;
        min-height: 250px;
    }

    .processing-animation {
        font-size: 2rem;
        margin-bottom: 0.75rem;
    }

    .processing-container h2 {
        font-size: 1.3rem;
        margin-bottom: 0.5rem;
    }

    .processing-container p {
        font-size: 0.85rem;
        margin-bottom: 0.75rem;
    }

    #processingScreen .progress-bar {
        max-width: 100%;
        height: 6px;
    }

    #processingScreen .progress-text {
        font-size: 0.75rem;
    }
}
