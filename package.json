{"name": "mcq-tf-desktop", "version": "1.0.0", "description": "Desktop application for generating multiple-choice and true/false questions", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["electron", "desktop", "questions", "mcq", "true-false", "education", "quiz"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.6.2", "canvas": "^3.1.0", "chalk": "^4.1.2", "crypto": "^1.0.1", "dotenv": "^16.4.7", "form-data": "^4.0.2", "mammoth": "^1.9.0", "node-fetch": "^3.3.2", "pdf-parse": "^1.1.1", "pdf-poppler": "^0.2.1", "pdfjs-dist": "^3.9.179", "sharp": "^0.33.5", "sqlite3": "^5.1.6", "tesseract.js": "^5.0.3", "uuid": "^11.1.0"}, "devDependencies": {"electron": "^32.0.0", "electron-builder": "^25.0.0", "nodemon": "^3.0.1"}, "build": {"appId": "com.mcqtf.desktop", "productName": "MCQ & TF Question Generator", "directories": {"output": "dist"}, "files": ["src/**/*", "data/**/*", "tessdata/**/*", "node_modules/**/*", "package.json"], "extraFiles": [{"from": "simple_extraction_service_fixed.py", "to": "simple_extraction_service_fixed.py"}, {"from": "requirements.txt", "to": "requirements.txt"}], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}