const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // File operations
  selectFile: () => ipcRenderer.invoke('select-file'),
  processFile: (filePath) => ipcRenderer.invoke('process-file', filePath),
  
  // Question generation
  generateQuestions: (content, type, count) => ipcRenderer.invoke('generate-questions', content, type, count),
  
  // Quiz operations
  startQuiz: (questions) => ipcRenderer.invoke('start-quiz', questions),
  submitAnswer: (questionIndex, answer) => ipcRenderer.invoke('submit-answer', questionIndex, answer),
  getQuizResults: () => ipcRenderer.invoke('get-quiz-results'),
  
  // Database operations
  saveQuizSession: (session) => ipcRenderer.invoke('save-quiz-session', session),
  getQuizHistory: () => ipc<PERSON>enderer.invoke('get-quiz-history'),
  getStatistics: () => ipcRenderer.invoke('get-statistics'),
  
  // Settings
  getSettings: () => ipcRenderer.invoke('get-settings'),
  saveSettings: (settings) => ipcRenderer.invoke('save-settings', settings),
  
  // Feedback
  submitFeedback: (feedback) => ipcRenderer.invoke('submit-feedback', feedback),
  
  // Menu events
  onMenuAction: (callback) => ipcRenderer.on('menu-action', callback),
  onFileSelected: (callback) => ipcRenderer.on('file-selected', callback),
  
  // Menu specific events
  onNewQuiz: (callback) => ipcRenderer.on('menu-new-quiz', callback),
  onGenerateMCQ: (callback) => ipcRenderer.on('menu-generate-mcq', callback),
  onGenerateTF: (callback) => ipcRenderer.on('menu-generate-tf', callback),
  onStartQuiz: (callback) => ipcRenderer.on('menu-start-quiz', callback),
  onSettings: (callback) => ipcRenderer.on('menu-settings', callback),
  onStatistics: (callback) => ipcRenderer.on('menu-statistics', callback),
  onHistory: (callback) => ipcRenderer.on('menu-history', callback),
  onAbout: (callback) => ipcRenderer.on('menu-about', callback),
  onHelp: (callback) => ipcRenderer.on('menu-help', callback),
  
  // Progress updates
  onProgressUpdate: (callback) => ipcRenderer.on('progress-update', callback),
  
  // Error handling
  onError: (callback) => ipcRenderer.on('error', callback),
  
  // Utility functions
  showNotification: (title, body) => ipcRenderer.invoke('show-notification', title, body),
  openExternal: (url) => ipcRenderer.invoke('open-external', url),
  
  // Remove listeners
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
});

// Expose some Node.js APIs that are safe to use in the renderer
contextBridge.exposeInMainWorld('nodeAPI', {
  platform: process.platform,
  versions: process.versions
});

// Expose application info
contextBridge.exposeInMainWorld('appInfo', {
  name: 'MCQ & TF Question Generator',
  version: '1.0.0',
  description: 'Desktop application for generating multiple-choice and true/false questions'
});
