// API related functions
const axios = require('axios');
const database = require('../database/database');
const fs = require('fs');
const path = require('path');
const config = require('../config');
const modelManager = require('./modelManager');
const logger = require('../utils/logger');
const crypto = require('crypto');

// Using OpenRouter API instead of getimg.ai which is giving 404 errors
const API_URL = "https://openrouter.ai/api/v1/chat/completions";

// Cache for in-progress requests to avoid duplicate processing
const inProgressRequests = new Map();

// Request queue management
const REQUEST_QUEUE = [];           // Queue for API requests
const MAX_CONCURRENT_REQUESTS = 3;  // Maximum concurrent requests
let ACTIVE_REQUESTS = 0;            // Current active requests count
const USER_PROCESSING = new Map();  // Track which users have requests in processing

// Track model usage to ensure balanced distribution
let modelUsage = {};
config.models.forEach(model => {
  modelUsage[model] = 0;
});

// Track which models have hit rate limits recently
let rateLimitedModels = {};

// Generate a unique request ID
function generateRequestId() {
  return crypto.randomBytes(4).toString('hex');
}

/**
 * Get the API key
 * @returns {Promise<string|null>} The API key or null if none available
 */
async function getNextApiKey() {
  try {
    // Check if database is initialized
    const db = database.db();
    
    if (!db) {
      console.log('Database not initialized, using environment key');
      return config.apiKey || null;
    }
    
    // First try to get keys from the database
    return new Promise((resolve, reject) => {
      // Check if the api_keys table exists
      db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='api_keys'", [], (err, row) => {
        if (err || !row) {
          console.log('API keys table not found, using environment key');
          resolve(config.apiKey || null);
          return;
        }
        
        // Table exists, query keys
        db.all('SELECT id, key, is_active FROM api_keys WHERE is_active = 1 LIMIT 1', [], async (err, rows) => {
          if (err) {
            console.error('Error fetching API keys:', err);
            resolve(config.apiKey || null);
            return;
          }
          
          // If no keys in database, check environment variables
          if (!rows || rows.length === 0) {
            console.log('No active API keys in database, using environment key');
            resolve(config.apiKey || null);
            return;
          }
            
            // Update last used timestamp for this key
            db.run('UPDATE api_keys SET last_used = ? WHERE id = ?', 
            [Date.now(), rows[0].id], (err) => {
                if (err) console.error('Error updating key usage timestamp:', err);
              });
            
          console.log('Using database API key');
          resolve(rows[0].key);
        });
      });
    });
  } catch (error) {
    console.error('Error in getNextApiKey:', error);
    return config.apiKey || null;
  }
}

/**
 * Get the current API key info
 * @returns {Object} The current key info
 */
function getCurrentKeyIndices() {
  return {
    environmentKeyAvailable: !!config.apiKey
  };
}

/**
 * Add an API key to the database
 * @param {string} apiKey The API key to add
 * @returns {Promise<boolean>} Success
 */
async function addApiKey(apiKey) {
  const db = database.db();
  
  return new Promise((resolve, reject) => {
    // Check if key already exists
    db.get('SELECT id FROM api_keys WHERE key = ?', [apiKey], (err, row) => {
      if (err) {
        console.error('Error checking for existing key:', err);
        reject(err);
        return;
      }
      
      if (row) {
        console.log('API key already exists in database');
        resolve(false);
        return;
      }
      
      // Insert new key
      db.run('INSERT INTO api_keys (key, is_active, last_used) VALUES (?, 1, ?)', 
        [apiKey, Date.now()], function(err) {
          if (err) {
            console.error('Error adding API key:', err);
            reject(err);
            return;
          }
          
          console.log(`Added new API key with ID ${this.lastID}`);
          resolve(true);
        });
    });
  });
}

/**
 * Verify all API keys and return stats
 * @returns {Promise<Object>} Object with total, valid, and invalid counts
 */
async function verifyApiKeys() {
  const stats = {
    total: 0,
    valid: 0,
    invalid: 0,
    databaseKeys: { total: 0, valid: 0, invalid: 0 },
    environmentKeys: { total: 0, valid: 0, invalid: 0 }
  };
  
  try {
    const db = database.db();
    
    // First verify database keys
    await new Promise((resolve, reject) => {
      db.all('SELECT id, key FROM api_keys', [], async (err, rows) => {
        if (err) {
          console.error('Error fetching API keys for verification:', err);
          resolve(); // Continue with environment keys
          return;
        }
        
        if (rows && rows.length > 0) {
          stats.total += rows.length;
          stats.databaseKeys.total = rows.length;
          
          // Test each key
          for (const row of rows) {
            try {
              const isValid = await testApiKey(row.key);
              if (isValid) {
                stats.valid++;
                stats.databaseKeys.valid++;
                // Update key status if needed
                db.run('UPDATE api_keys SET is_active = 1 WHERE id = ?', [row.id]);
              } else {
                stats.invalid++;
                stats.databaseKeys.invalid++;
                // Mark key as inactive
                db.run('UPDATE api_keys SET is_active = 0 WHERE id = ?', [row.id]);
              }
            } catch (error) {
              console.error(`Error testing API key ${row.id}:`, error);
              stats.invalid++;
              stats.databaseKeys.invalid++;
              // Mark key as inactive
              db.run('UPDATE api_keys SET is_active = 0 WHERE id = ?', [row.id]);
            }
          }
        }
        
        resolve();
      });
    });
    
    // Now check environment variable keys
    if (config.apiKeys.length > 0) {
      stats.total += config.apiKeys.length;
      stats.environmentKeys.total = config.apiKeys.length;
      
      // Test each key
      for (const key of config.apiKeys) {
        try {
          const isValid = await testApiKey(key);
          if (isValid) {
            stats.valid++;
            stats.environmentKeys.valid++;
          } else {
            stats.invalid++;
            stats.environmentKeys.invalid++;
          }
        } catch (error) {
          console.error(`Error testing API key from environment:`, error);
          stats.invalid++;
          stats.environmentKeys.invalid++;
        }
      }
    }
    
    return stats;
  } catch (error) {
    console.error('Error in verifyApiKeys:', error);
    return stats;
  }
}

/**
 * Test if an API key is valid
 * @param {string} apiKey - API key to test
 * @returns {Promise<boolean>} Whether the key is valid
 */
async function testApiKey(apiKey) {
  try {
    // Make a minimal API request to check if the key works
    const response = await axios.post(API_URL, {
      model: 'openai/gpt-3.5-turbo-instruct:free',
      messages: [{ role: 'user', content: 'Hi' }],
      max_tokens: 5
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'HTTP-Referer': 'https://telegram-mcq-tf-bot.com',
        'X-Title': 'Telegram MCQ/TF Question Generator'
      },
      timeout: 10000 // 10s timeout for key testing
    });
    
    return response.status === 200;
  } catch (error) {
    logger.error(`API key test failed: ${error.message}`);
    return false;
  }
}

/**
 * Get available models from config
 * @returns {Array} Array of model strings
 */
function getModels() {
  return config.models;
}

/**
 * Select an appropriate model using a balanced round-robin strategy
 * @returns {string} The model to use
 */
function selectModel() {
  // Reset rate limit status every hour to allow retry
  const now = Date.now();
  Object.keys(rateLimitedModels).forEach(model => {
    // If it's been more than 1 hour since the rate limit, clear it
    if (now - rateLimitedModels[model] > 60 * 60 * 1000) {
      delete rateLimitedModels[model];
      logger.info(`Model ${model} rate limit expired, making available again`);
    }
  });

  // Filter out any rate-limited models
  const availableModels = config.models.filter(model => !rateLimitedModels[model]);
  
  // If all models are rate-limited, use the one that was rate-limited the longest time ago
  if (availableModels.length === 0) {
    logger.warn('All models are currently rate-limited, using the oldest rate-limited model');
    const oldestRateLimitedModel = Object.keys(rateLimitedModels).reduce((oldest, model) => {
      return (!oldest || rateLimitedModels[model] < rateLimitedModels[oldest]) ? model : oldest;
    }, null);
    
    // Log this special case clearly
    logger.warn(`Using rate-limited model ${oldestRateLimitedModel} because all models are rate-limited`);
    return oldestRateLimitedModel || config.models[0]; // Fallback to first model if something goes wrong
  }
  
  // Find the model with the lowest usage count
  let selectedModel = availableModels[0];
  let lowestUsage = modelUsage[selectedModel] || 0;
  
  availableModels.forEach(model => {
    const usage = modelUsage[model] || 0;
    if (usage < lowestUsage) {
      lowestUsage = usage;
      selectedModel = model;
    }
  });
  
  // Increment usage count for the selected model
  modelUsage[selectedModel] = (modelUsage[selectedModel] || 0) + 1;
  
  // Enhanced logging
  logger.debug(`Selected model ${selectedModel} (used ${modelUsage[selectedModel]} times)`);
  logger.debug(`Available models: ${availableModels.length}/${config.models.length}, Rate-limited models: ${Object.keys(rateLimitedModels).length}`);
  
  return selectedModel;
}

/**
 * Mark a model as rate-limited so we can avoid it for a while
 * @param {string} model The model that hit a rate limit
 * @param {number} durationMs - Optional: duration in milliseconds to mark as rate-limited (default: 1 hour)
 */
function markModelAsRateLimited(model, durationMs = 60 * 60 * 1000) {
  if (!model) return;
  
  rateLimitedModels[model] = Date.now();
  
  // Log model rate limit
  const duration = Math.round(durationMs / 60 / 1000);
  logger.warn(`Marked model ${model} as rate-limited, will avoid using it for ${duration} minutes`);
  
  // Also log all currently rate-limited models for debugging
  const limitedModels = Object.keys(rateLimitedModels);
  logger.debug(`Currently rate-limited models (${limitedModels.length}): ${limitedModels.join(', ')}`);
}

/**
 * Record model usage statistics in the database
 * @param {string} model Model name
 * @param {boolean} success Whether the request was successful
 * @param {number} duration Time taken for the request in ms
 */
function recordModelStats(model, success, duration) {
  try {
    // Get database instance from the database module
    const db = database.db();
    
    if (!db) {
      logger.warn('Database not initialized for model stats');
      return;
    }
    
    // Ensure we have access to run function
    if (typeof db.run !== 'function') {
      logger.error('Database instance does not have run method');
      return;
    }
    
    const timestamp = Date.now();
    
    // Insert a new record
    db.run(
      'INSERT INTO model_stats (model, success, duration, timestamp) VALUES (?, ?, ?, ?)',
      [model, success ? 1 : 0, duration, timestamp],
      function(err) {
        if (err) {
          logger.error(`Error recording model stats: ${err.message}`);
        } else {
          logger.debug(`Recorded stats for model ${model}: success=${success}, duration=${duration}ms`);
        }
      }
    );
  } catch (error) {
    logger.error(`Error in recordModelStats: ${error.message}`);
  }
}

/**
 * Create system message based on question type
 * @param {string} type - Question type (MCQ or TF)
 * @param {number} count - Number of questions
 * @returns {object} System message object
 */
function createSystemMessage(type, count) {
  if (type === 'MCQ') {
    return 'You are an expert educator specialized in creating multiple-choice questions. Your output must follow the exact format specified.';
  } else {
    return 'You are an expert educator specialized in creating true/false questions. Your output must follow the exact format specified.';
  }
}

/**
 * Create user message based on question type
 * @param {string} text - Text to generate questions from
 * @param {string} type - Question type (MCQ or TF)
 * @param {number} count - Number of questions
 * @returns {string} User message
 */
function createUserMessage(text, type, count) {
  if (type === 'MCQ') {
    return `Create ${count} multiple-choice (MCQ) questions based on the following text. 
      Each question should have exactly 4 options labeled A, B, C, and D.

      VERY IMPORTANT: Your response MUST be a JSON array with this exact structure:
      [
        {
          "question": "Question text",
          "options": ["Option A", "Option B", "Option C", "Option D"],
          "answer": "A", 
          "explanation": "Brief explanation of the answer"
        }
      ]
      
      DO NOT include any text before or after the JSON array. The response must start with [ and end with ].
      DO NOT wrap the JSON in markdown code blocks or backticks.
      DO NOT include any explanation text or formatting outside the JSON structure.
      
      When creating questions:
      1. Make sure each question tests understanding of concepts in the text
      2. Keep questions clear and concise
      3. Make all options plausible but only one correct
      4. Avoid overly similar options
      5. Provide brief, helpful explanations
      6. DO NOT create questions about the number of pages or document structure
      7. DO NOT refer to page numbers or document format in your questions
      8. Focus ONLY on the content and subject matter in the text
      
      Text: ${text.substring(0, 12000)}`;
      } else {
    return `Create ${count} true/false questions based on the following text.

      VERY IMPORTANT: Your response MUST be a JSON array with this exact structure:
      [
        {
          "question": "Question text", 
          "answer": true,
          "explanation": "Brief explanation of why the statement is true or false"
        }
      ]
      
      DO NOT include any text before or after the JSON array. The response must start with [ and end with ].
      DO NOT wrap the JSON in markdown code blocks or backticks.
      DO NOT include any explanation text or formatting outside the JSON structure.
      
      When creating questions:
      1. Make sure each question tests understanding of concepts in the text
      2. Avoid questions that are too obvious
      3. Use specific details from the text
      4. Mix true and false questions evenly
      5. Make explanations clear and helpful
      6. DO NOT create questions about the number of pages or document structure
      7. DO NOT refer to page numbers or document format in your questions
      8. Focus ONLY on the content and subject matter in the text
      
      Text: ${text.substring(0, 12000)}`;
  }
}

/**
 * Process the next request in the queue if there's capacity
 * @private
 */
function processNextInQueue() {
  // If we're at capacity or queue is empty, do nothing
  if (ACTIVE_REQUESTS >= MAX_CONCURRENT_REQUESTS || REQUEST_QUEUE.length === 0) {
    return;
  }
  
  // Find the highest priority request (prioritize users who don't have active requests)
  let highestPriorityIndex = 0;
  
  // First check for any user who doesn't already have an active request
  for (let i = 0; i < REQUEST_QUEUE.length; i++) {
    const req = REQUEST_QUEUE[i];
    if (req.userId && !USER_PROCESSING.has(req.userId)) {
      highestPriorityIndex = i;
      break;
    }
  }
  
  // Take the selected request from the queue
  const nextRequest = REQUEST_QUEUE.splice(highestPriorityIndex, 1)[0];
  ACTIVE_REQUESTS++;
  
  // Track this user's request
  if (nextRequest.userId) {
    USER_PROCESSING.set(nextRequest.userId, Date.now());
    logger.info(`Processing request for user ${nextRequest.userId} (queue length: ${REQUEST_QUEUE.length})`);
  }
  
  // Execute the request
  nextRequest.execute()
    .then(result => {
      nextRequest.resolve(result);
    })
    .catch(error => {
      nextRequest.reject(error);
    })
    .finally(() => {
      // Cleanup
      ACTIVE_REQUESTS--;
      
      // Remove user from processing map
      if (nextRequest.userId) {
        USER_PROCESSING.delete(nextRequest.userId);
        logger.info(`Completed request for user ${nextRequest.userId} (remaining in queue: ${REQUEST_QUEUE.length})`);
      }
      
      // Process next request
      processNextInQueue();
    });
}

/**
 * Queue a request to be executed when capacity is available
 * @param {Function} requestFn - Function that returns a promise when executed
 * @param {string} userId - User ID for tracking
 * @returns {Promise} - Promise that resolves when request completes
 * @private
 */
function queueRequest(requestFn, userId) {
  return new Promise((resolve, reject) => {
    // Create request object
    const request = {
      execute: requestFn,
      resolve: resolve,
      reject: reject,
      userId: userId,
      timestamp: Date.now()
    };
    
    // Add to the queue
    REQUEST_QUEUE.push(request);
    logger.info(`Queued request for user ${userId || 'unknown'} (queue length: ${REQUEST_QUEUE.length})`);
    
    // Try to process the queue
    processNextInQueue();
  });
}

// Define a more robust function to extract content from API responses
function extractContentFromResponse(response, requestId) {
  try {
    // Log raw response structure for debugging
    const responseKeys = response.data ? Object.keys(response.data) : [];
    logger.debug(`[${requestId}] Response keys: ${JSON.stringify(responseKeys)}`);
    
    // Standard OpenAI/ChatGPT response format
    if (response.data && response.data.choices && 
        response.data.choices.length > 0 && 
        response.data.choices[0].message &&
        response.data.choices[0].message.content) {
      return response.data.choices[0].message.content;
    }
    
    // Alternative format for some models
    if (response.data && response.data.message && response.data.message.content) {
      return response.data.message.content;
    }
    
    // Direct content in message
    if (response.data && response.data.message) {
      // Check if message is an object or string
      if (typeof response.data.message === 'object' && response.data.message.content) {
        return response.data.message.content;
      }
      return response.data.message;
    }
    
    // Simple text field
    if (response.data && typeof response.data.text === 'string') {
      return response.data.text;
    }
    
    // Output field (Llama/Mistral sometimes uses this)
    if (response.data && typeof response.data.output === 'string') {
      return response.data.output;
    }
    
    // Llama, Nemotron, Claude format
    if (response.data && response.data.generated_text) {
      return response.data.generated_text;
    }
    
    // Nemotron format
    if (response.data && response.data.results && response.data.results.length > 0) {
      if (response.data.results[0].text) {
        return response.data.results[0].text;
      }
      if (response.data.results[0].content) {
        return response.data.results[0].content;
      }
      if (response.data.results[0].generation) {
        return response.data.results[0].generation;
      }
    }
    
    // Qwen model format
    if (response.data && response.data.choices && response.data.choices.length > 0) {
      // Check multiple possible structures
      const choice = response.data.choices[0];
      
      if (choice.text) {
        return choice.text;
      }
      
      if (choice.content) {
        return choice.content;
      }
      
      if (choice.message && typeof choice.message === 'object') {
        if (choice.message.content) {
          return choice.message.content;
        }
        if (choice.message.text) {
          return choice.message.text;
        }
      }
    }
    
    // Additional Llama/Nvidia format
    if (response.data && response.data.response && typeof response.data.response === 'string') {
      return response.data.response;
    }
    
    // Anthropic/Claude format
    if (response.data && response.data.content && typeof response.data.content === 'string') {
      return response.data.content;
    }
    
    // Cohere format
    if (response.data && response.data.generations && 
        Array.isArray(response.data.generations) && 
        response.data.generations.length > 0 && 
        response.data.generations[0].text) {
      return response.data.generations[0].text;
    }
    
    // Just text in response
    if (response.data && typeof response.data === 'string') {
      return response.data;
    }
    
    // Fallback: try to find any text content in the response
    if (response.data) {
      // Try to find the first string property that's longer than 50 chars
      for (const key in response.data) {
        if (typeof response.data[key] === 'string' && response.data[key].length > 50) {
          logger.debug(`[${requestId}] Using fallback content from field: ${key}`);
          return response.data[key];
        }
        
        // Check one level deeper
        if (typeof response.data[key] === 'object' && response.data[key]) {
          for (const subKey in response.data[key]) {
            if (typeof response.data[key][subKey] === 'string' && response.data[key][subKey].length > 50) {
              logger.debug(`[${requestId}] Using fallback content from nested field: ${key}.${subKey}`);
              return response.data[key][subKey];
            }
          }
        }
      }
    }
    
    // Add extreme fallback - just stringify and return the full response
    if (response.data) {
      try {
        const stringified = JSON.stringify(response.data);
        if (stringified && stringified.length > 10) {
          logger.debug(`[${requestId}] Last resort: returning stringified response`);
          return stringified;
        }
      } catch (stringifyError) {
        logger.error(`[${requestId}] Error stringifying response: ${stringifyError.message}`);
      }
    }
    
    // Last resort: try to extract any JSON-like content from the response
    if (response.data && JSON.stringify(response.data).length > 100) {
      const responseStr = JSON.stringify(response.data);
      const jsonMatch = responseStr.match(/\[\s*{[\s\S]*}\s*\]/);
      if (jsonMatch) {
        logger.debug(`[${requestId}] Extracted JSON array from response`);
        return jsonMatch[0];
      }
    }
    
    // Very last resort: stringify the whole response
    logger.debug(`[${requestId}] Response structure: ${JSON.stringify(Object.keys(response.data || {}))}`);
    throw new Error('Unexpected API response format');
  } catch (error) {
    logger.error(`[${requestId}] Error extracting content: ${error.message}`);
    throw error;
  }
}

/**
 * Make a request to the API endpoint through the model manager
 * @param {string|object} model - Model to use
 * @param {string} systemMessage - System message
 * @param {string} userMessage - User message
 * @param {number|string} userId - User ID for request prioritization
 * @returns {Promise<string>} Response content
 */
async function makeRequest(model, systemMessage, userMessage, userId = null) {
  try {
    // Generate a unique request hash based on user message content
    // This helps deduplicate identical requests
    const requestHash = crypto
      .createHash('md5')
      .update(`${model}:${systemMessage}:${userMessage.substring(0, 100)}`)
      .digest('hex');
    
    // Generate a short request ID for logging
    const requestId = generateRequestId();
    
    // Single log entry for request initiation
    logger.api(`[${requestId}] Request queued: model=${model}, userId=${userId || 'anonymous'}, hash=${requestHash.substring(0, 6)}`);
   
    // Check if an identical request is already in progress
    if (inProgressRequests.has(requestHash)) {
      logger.api(`[${requestId}] Duplicate request detected. Waiting for existing request to complete.`);
      return await inProgressRequests.get(requestHash);
    }
    
    // Create a promise for this request that we can add to the in-progress map
    const requestPromise = new Promise(async (resolve, reject) => {
      try {
        // Define the request function to be executed through the model manager
        const makeApiRequest = async () => {
          try {
            // Log only when actually executing the request, not when queueing
            logger.api(`[${requestId}] Executing API request to ${model}`);
            
            // Set timeout based on model - some models are much slower
            const getTimeoutForModel = (modelName) => {
              // Slower models - extra short timeout to avoid delays
              if (modelName.includes('llama') || modelName.includes('nemotron')) {
                return 15000; // 15 seconds only for these problematic models
              }
              // Medium speed models
              if (modelName.includes('claude') || modelName.includes('gpt-4') || 
                  modelName.includes('mistral') || modelName.includes('deepseek')) {
                return 45000; // 45 seconds
              }
              // Qwen model is faster than other vision models
              if (modelName.includes('qwen')) {
                return 25000; // 25 seconds
              }
              // Meta Llama 4 models
              if (modelName.includes('meta-llama')) {
                return 20000; // 20 seconds for Meta's Llama 4
              }
              // Moonshot model
              if (modelName.includes('moonshot')) {
                return 30000; // 30 seconds for Moonshot
              }
              // Fast models
              return 30000; // 30 seconds for gemini, gpt-3.5, etc.
            };
            
            // Calculate timeout based on model
            const modelTimeout = getTimeoutForModel(model);
            
            // Prepare request payload
            const payload = {
              model: model,
              messages: [
                { role: "system", content: systemMessage },
                { role: "user", content: userMessage }
              ],
              temperature: 0.7,
              max_tokens: 2048
            };
            
            // Only add response_format for models that support it
            // Some models like Llama/Gemma may not support the response_format parameter
            if (model.includes('gpt') || model.includes('gemini')) {
              payload.response_format = { type: "json_object" };
            }
            
            const startTime = Date.now();
            const apiKey = await getNextApiKey();
            
            if (!apiKey) {
              throw new Error('No API key available');
            }
            
            let response;
            try {
              response = await axios.post(API_URL, payload, {
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`,
                'HTTP-Referer': 'https://telegram-mcq-tf-bot.com',
                'X-Title': 'Telegram MCQ/TF Question Generator'
              },
              timeout: modelTimeout // Use model-specific timeout instead of config.requestTimeout
            });
            } catch (axiosError) {
              // Check specifically for rate limit errors in the response
              if (axiosError.response) {
                const statusCode = axiosError.response.status;
                // 429 is the standard rate limit status code
                if (statusCode === 429) {
                  logger.error(`[${requestId}] Rate limit error (429) from API`);
                  throw new Error('Rate limit exceeded');
                }
                
                // Check response data for rate limit messages
                if (axiosError.response.data) {
                  const responseData = axiosError.response.data;
                  if (typeof responseData === 'string' && (responseData.includes('rate limit') || responseData.includes('Rate limit'))) {
                    logger.error(`[${requestId}] Rate limit mentioned in error response`);
                    throw new Error(`Rate limit: ${responseData.substring(0, 50)}...`);
                  } else if (responseData.error && typeof responseData.error === 'string' && (responseData.error.includes('rate limit') || responseData.error.includes('Rate limit'))) {
                    logger.error(`[${requestId}] Rate limit in error object`);
                    throw new Error(`Rate limit: ${responseData.error.substring(0, 50)}...`);
                  } else if (responseData.error && typeof responseData.error === 'object') {
                    // Handle case where error is an object
                    const errorMessage = responseData.error.message || JSON.stringify(responseData.error);
                    if (typeof errorMessage === 'string' && (errorMessage.includes('rate limit') || errorMessage.includes('Rate limit'))) {
                      logger.error(`[${requestId}] Rate limit in complex error object`);
                      throw new Error(`Rate limit: ${errorMessage.substring(0, 50)}...`);
                    }
                  }
                }
              }
              
              // Rethrow the original error if not a rate limit
              throw axiosError;
            }
            
            const duration = Date.now() - startTime;
            logger.api(`[${requestId}] Received response in ${duration}ms`);
            
            // Use the robust extraction function
            const responseContent = extractContentFromResponse(response, requestId);
            
            // Check for rate limit in the response content
            if (responseContent.includes('Rate limit') || responseContent.includes('rate limit')) {
              logger.error(`[${requestId}] Rate limit detected in response: ${responseContent.substring(0, 100)}...`);
              throw new Error(`Rate limit detected: ${responseContent.substring(0, 50)}...`);
            }
            
            // Record successful stats
            recordModelStats(model, true, duration);
            
            return responseContent;
          } catch (error) {
            logger.error(`[${requestId}] Request failed: ${error.message}`);
            // Record failed stats
            recordModelStats(model, false, 0);
            throw error;
          }
        };
        
        // Queue the API request with the user ID for priority
        const result = await queueRequest(makeApiRequest, userId);
        logger.api(`[${requestId}] Request complete and result returned`);
        resolve(result);
      } catch (error) {
        logger.error(`[${requestId}] Request failed: ${error.message}`);
        // Record failed stats
        recordModelStats(model, false, 0);
        reject(error);
      } finally {
        // Remove from in-progress map when done
        inProgressRequests.delete(requestHash);
      }
    });
    
    // Add to in-progress map
    inProgressRequests.set(requestHash, requestPromise);
    
    // Return the promise result
    return await requestPromise;
  } catch (error) {
    logger.error(`API request failed: ${error.message}`);
    throw error;
  }
}

/**
 * Generate questions using the specified AI model
 * @param {string} text Text to generate questions from
 * @param {string} type Question type ('MCQ' or 'TF')
 * @param {number} count Number of questions to generate
 * @param {number} maxRetries Maximum number of retries
 * @param {boolean} isScanned Whether the text is from a scanned document
 * @param {number|string} userId User ID for request prioritization
 * @returns {Promise<Array>} Array of question objects
 */
async function generateQuestionsFromAPI(text, type, count = 15, retries = 3, isScanned = false, userId = null, contentType = 'text', preferredModel = 'auto') {
  logger.info(`API Service: generateQuestionsFromAPI called with preferredModel: ${preferredModel}`);
  // Generate a request ID for logging
  const requestId = generateRequestId();
  
  // Keep track of attempts
  let attempts = 0;
  let modelAttempts = {};
  
  // Start timer
  const startTime = Date.now();
  
  // Keep track of which models we've already tried
  const triedModels = new Set();
  
  // Try to generate questions, with retries
  let lastError = null;
  while (attempts <= retries) {
    try {
      attempts++;
      
      // Select the appropriate model based on user preference
      let model;

      if (preferredModel && preferredModel !== 'auto') {
        // Always use the user's preferred model when specified
        model = preferredModel;
        logger.info(`[${requestId}] Using user-preferred model: ${model} (attempt ${attempts})`);
      } else if (attempts === 1) {
        // For auto selection on first attempt, use our specialized model selector
        model = selectBestModelForContent(contentType, isScanned);
        logger.info(`[${requestId}] Auto-selected model: ${model}`);
      } else {
        // For retry attempts with auto selection, use our model selection logic
        model = selectModel();

        // If we've already tried this model in a previous attempt and have other options,
        // try to use a different model if possible
        if (triedModels.has(model) && triedModels.size < config.models.length) {
          // Find a model we haven't tried yet
          const unusedModel = config.models.find(m => !triedModels.has(m));
          if (unusedModel) {
            model = unusedModel;
          }
        }
        logger.info(`[${requestId}] Auto-retry with model: ${model}`);
      }
      
      // Keep track of this model attempt
      triedModels.add(model);
      modelAttempts[model] = (modelAttempts[model] || 0) + 1;
      
      logger.info(`[${requestId}] Generating ${count} ${type} questions using model: ${model}, userId: ${userId}`);
      
      // Create system and user messages
      const systemMessage = createSystemMessage(type, count);
      const userMessage = createUserMessage(text, type, count);
      
      try {
      // Make the request to the API endpoint - pass userId for priority handling
      const responseContent = await makeRequest(model, systemMessage, userMessage, userId);
      
      // Process the response - attempt several different parsing strategies
      try {
        const questions = JSON.parse(responseContent);
        
        if (Array.isArray(questions) && questions.length > 0) {
          logger.info(`[${requestId}] Successfully parsed ${questions.length} questions from JSON response`);
          return questions;
        }
      } catch (parseError) {
        // This was expected - continue to other parsing methods
          logger.debug(`[${requestId}] Initial JSON parse failed: ${parseError.message}`);
        }
        
        // Before continuing, check if the response contains error keywords that could indicate model issues
        const errorKeywords = ['error', 'rate limit', 'quota', 'exceeded', 'unavailable', 'maintenance', 'capacity', 'busy', 'too many requests'];
        const rateLimitKeywords = ['rate limit', 'quota', 'capacity', 'too many requests', 'try again later', 'exceeded', 'error_limit'];
        
        if (typeof responseContent === 'string') {
          const lcResponse = responseContent.toLowerCase();
          
          // First check for rate limit specifically
          for (const keyword of rateLimitKeywords) {
            if (lcResponse.includes(keyword)) {
              logger.warn(`[${requestId}] Rate limit keyword detected: ${keyword}`);
              markModelAsRateLimited(model);
              throw new Error(`Rate limit detected: ${responseContent.substring(0, 100)}...`);
            }
          }
          
          // Then check for other errors
          for (const keyword of errorKeywords) {
            if (lcResponse.includes(keyword)) {
              logger.warn(`[${requestId}] Response contains error keyword: ${keyword}`);
              
              // If it also has 'limit' or 'capacity' anywhere, treat as rate limit
              if (lcResponse.includes('limit') || lcResponse.includes('capacity') || lcResponse.includes('try again')) {
                markModelAsRateLimited(model);
                throw new Error(`Potential rate limit detected: ${responseContent.substring(0, 100)}...`);
              }
            }
          }
      }
      
      // Store the first part of the response for error logging
      const responsePreview = responseContent.substring(0, 200) + '...';
      
      // Second attempt: Try to repair the JSON
      const repairedJson = repairBrokenJson(responseContent);
      try {
        const parsedQuestions = JSON.parse(repairedJson);
        if (Array.isArray(parsedQuestions) && parsedQuestions.length > 0) {
          logger.info(`[${requestId}] Successfully parsed ${parsedQuestions.length} questions after repairing JSON`);
          return parsedQuestions;
        }
      } catch (repairError) {
        // Continue to the next method
        logger.debug(`[${requestId}] Repair attempt failed: ${repairError.message}`);
      }
      
      // New aggressive repair approach - try to extract just the valid parts
      const bruteForceQuestions = extractQuestionsByBruteForce(responseContent, type);
      if (bruteForceQuestions && bruteForceQuestions.length > 0) {
        logger.info(`[${requestId}] Successfully extracted ${bruteForceQuestions.length} questions using brute force extraction`);
        return bruteForceQuestions;
      }
      
      // Third attempt: Try to parse the text response
      const textParsedQuestions = parseTextResponse(responseContent, type);
      
      if (textParsedQuestions && textParsedQuestions.length > 0) {
        logger.info(`[${requestId}] Successfully parsed ${textParsedQuestions.length} questions from text response`);
        return textParsedQuestions;
      }
      
      // Fourth attempt: Last resort - try to extract something that looks like questions
      // even if we can't parse the full structure
      const emergencyQuestions = extractEmergencyQuestions(responseContent, type);
      if (emergencyQuestions && emergencyQuestions.length > 0) {
        logger.info(`[${requestId}] Successfully extracted ${emergencyQuestions.length} emergency questions from broken response`);
        return emergencyQuestions;
      }
      
      // If we've reached this point, we couldn't parse the response in any way
      logger.error(`[${requestId}] Failed to parse response. Preview: ${responsePreview}`);
      throw new Error(`Failed to parse AI response into questions`);
    } catch (error) {
        const errorMessage = error.message || 'Unknown error';
        lastError = error;
        
        // Enhanced error detection for rate limits and other API issues
        const rateLimitIndicators = [
          'rate limit', 'Rate limit', 'quota', 'capacity', 'error_limit', 
          'too many requests', 'try again later', 'exceeded'
        ];
        
        // Check if this is a rate limit error or something similar
        const isRateLimit = rateLimitIndicators.some(indicator => 
          errorMessage.toLowerCase().includes(indicator.toLowerCase())
        );
        
        // Also check for HTTP 429 status which indicates rate limiting
        const isHttp429 = errorMessage.includes('429') || errorMessage.includes('Too Many Requests');
        
        if (isRateLimit || isHttp429 || errorMessage.includes('error') && errorMessage.includes('limit')) {
          logger.warn(`[${requestId}] Rate limit detected for model ${model}. Marking as rate-limited.`);
          markModelAsRateLimited(model);
      
          // Only auto-rotate if user selected "auto" mode
          if (preferredModel === 'auto') {
            // If we have other models available that we haven't tried, continue with a different model
            const availableModels = config.models.filter(m => !triedModels.has(m) && !isModelRateLimited(m));

            if (availableModels.length > 0) {
              // Select a model that isn't rate limited and hasn't been tried yet
              const nextModel = availableModels[0];
              logger.info(`[${requestId}] Auto mode: Will try with a different model: ${nextModel}. ${availableModels.length} models left to try.`);
              continue; // Skip the waiting period and immediately try with a different model
            } else {
              logger.warn(`[${requestId}] Auto mode: No more untried, non-rate-limited models available.`);
            }
          } else {
            // User selected a specific model - don't auto-rotate, let them decide
            logger.info(`[${requestId}] User selected specific model ${preferredModel} - not auto-rotating. User can choose to try different model.`);
          }
        }
        
        // Rethrow the error if this is the last attempt
      if (attempts > retries) {
          throw new Error(`Failed to generate questions after ${attempts} attempts: ${errorMessage}`);
      }
      
      // Otherwise, log and retry
        logger.warn(`[${requestId}] Attempt ${attempts} failed: ${errorMessage}. Retrying...`);
      
      // Brief delay before retrying - back off with each retry
      await new Promise(resolve => setTimeout(resolve, 1000 * attempts));
    }
    } catch (outerError) {
      // Log the outer error and continue to the next attempt
      logger.error(`Error in question generation attempt ${attempts}: ${outerError.message}`);
      lastError = outerError;
      
      // If this was the last attempt, we'll let the while loop end naturally
      // and the final error will be thrown outside the loop
    }
  }
  
  // If we get here, all attempts failed
  const elapsed = Date.now() - startTime;
  const errorMessage = lastError ? lastError.message : 'Unknown error';
  throw new Error(`Failed to generate questions after ${retries + 1} attempts in ${Math.round(elapsed/1000)}s. Last error: ${errorMessage}`);
}

/**
 * Extract questions by brute force pattern matching
 * This is the most aggressive approach that doesn't rely on JSON.parse at all
 * @param {string} text - Raw response text
 * @param {string} type - Question type (MCQ or TF)
 * @returns {Array} - Array of question objects
 */
function extractQuestionsByBruteForce(text, type) {
  try {
    logger.debug('Attempting brute force question extraction');
    const questions = [];
    
    // First, clean up any code block markers and additional text
    text = text.replace(/```json/g, '').replace(/```/g, '');
    
    // Remove common prefixes that might appear before the JSON array
    const commonPrefixes = [
      "Here are your questions:",
      "Here are the questions:",
      "I've generated the following questions:",
      "Here's the list of questions:",
      "Generated questions:",
      "Questions:"
    ];
    
    for (const prefix of commonPrefixes) {
      if (text.includes(prefix)) {
        const prefixIndex = text.indexOf(prefix);
        const remainingText = text.substring(prefixIndex + prefix.length);
        // Only replace if what remains looks like it might contain JSON
        if (remainingText.includes('{') && remainingText.includes('}')) {
          text = remainingText;
        }
      }
    }
    
    // Check if text starts with [
    if (!text.trim().startsWith('[')) {
      // Try to find the array start
      const arrayStart = text.indexOf('[');
      if (arrayStart >= 0) {
        text = text.substring(arrayStart);
      }
    }
    
    // Process each question block independently
    // First try to split on },{ pattern
    let questionBlocks = text.split(/},\s*{/);
    
    // If we didn't get many blocks, try another approach - look for "question": patterns
    if (questionBlocks.length < 3) {
      questionBlocks = [];
      const questionMatches = [...text.matchAll(/"question":\s*"([^"]+)"/g)];
      
      for (const match of questionMatches) {
        // Find the start of this question object
        const matchPos = match.index;
        let blockStart = text.lastIndexOf('{', matchPos);
        
        if (blockStart === -1) continue;
        
        // Find the end of this object - look for the next }
        let blockEnd = text.indexOf('}', matchPos);
        if (blockEnd === -1) {
          // If no closing brace, just take the rest of text
          blockEnd = text.length;
          } else {
          blockEnd++; // Include the closing brace
        }
        
        const block = text.substring(blockStart, blockEnd);
        questionBlocks.push(block);
      }
    }
    
    for (let i = 0; i < questionBlocks.length; i++) {
      try {
        let block = questionBlocks[i].trim();
        
        // Skip array markers
        if (block === '[' || block === ']') continue;
        
        // If this is the first block, remove the opening [
        if (i === 0 && block.startsWith('[')) {
          block = block.substring(1).trim();
        } else if (i > 0 && !block.startsWith('{')) {
          // If not the first block and doesn't start with {, add it
          block = '{' + block;
        }
        
        // If this is the last block, remove the closing ]
        if (i === questionBlocks.length - 1 && block.endsWith(']')) {
          block = block.substring(0, block.length - 1).trim();
        }
        
        // Make sure the block starts with {
        if (!block.startsWith('{')) {
          block = '{' + block;
        }
        
        // Make sure the block ends with }
        if (!block.endsWith('}')) {
          block = block + '}';
        }
        
        // Extract question text
        const questionMatch = block.match(/"question":\s*"([^"]+)"/);
        if (!questionMatch) continue;
        const questionText = questionMatch[1].trim();
        
        // Enhanced function to extract string value safely
        const extractStringValue = (blockText, fieldName) => {
          // Try standard pattern first
          const standardMatch = blockText.match(new RegExp(`"${fieldName}":\\s*"([^"]+)"`, 's'));
          
          if (standardMatch) return standardMatch[1].trim();
          
          // Try to handle multi-line string with a more aggressive approach
          const startPos = blockText.indexOf(`"${fieldName}":`);
          if (startPos === -1) return null;
          
          // Find the first quote after field name
          const quoteStart = blockText.indexOf('"', startPos + fieldName.length + 2);
          if (quoteStart === -1) return null;
          
          // Now find the end of this string - this is tricky with potential escaped quotes
          let quoteEnd = -1;
          let inEscape = false;
          
          for (let pos = quoteStart + 1; pos < blockText.length; pos++) {
            const char = blockText[pos];
            
            if (inEscape) {
              inEscape = false;
              continue;
            }
            
            if (char === '\\') {
              inEscape = true;
              continue;
            }
            
            if (char === '"') {
              quoteEnd = pos;
              break;
            }
          }
          
          if (quoteEnd === -1) {
            // If no end quote found, try to get text until next comma or closing brace
            const commaPos = blockText.indexOf(',', quoteStart);
            const bracePos = blockText.indexOf('}', quoteStart);
            
            if (commaPos !== -1 && (bracePos === -1 || commaPos < bracePos)) {
              return blockText.substring(quoteStart + 1, commaPos).trim();
            } else if (bracePos !== -1) {
              return blockText.substring(quoteStart + 1, bracePos).trim();
            }
            
            return null;
          }
          
          return blockText.substring(quoteStart + 1, quoteEnd).trim();
        };
        
        // Build the question object based on type
        if (type === 'MCQ') {
          // Try to extract options
          const options = [];
          const optionsMatch = block.match(/"options":\s*\[(.*?)\]/s);
          
          if (optionsMatch) {
            // Extract individual options
            const optionsText = optionsMatch[1];
            const optionMatches = [...optionsText.matchAll(/"([^"]+)"/g)];
            
            for (let j = 0; j < Math.min(optionMatches.length, 4); j++) {
              options.push(optionMatches[j][1]);
            }
            
            // Fill in missing options if needed
            while (options.length < 4) {
              options.push(`Option ${String.fromCharCode(65 + options.length)}`);
            }
          } else {
            // Default options if none found
            options.push('Option A', 'Option B', 'Option C', 'Option D');
          }
          
          // Extract answer
          let answer = 'A'; // Default
          const answerMatch = block.match(/"answer":\s*"([A-D])"/);
          if (answerMatch) {
            answer = answerMatch[1];
          }
          
          // Extract explanation using enhanced function
          const explanation = extractStringValue(block, 'explanation') || 'No explanation provided';
          
          // Add the question
          questions.push({
            question: questionText,
            options: options,
            answer: answer,
            explanation: explanation
          });
        } else {
          // TF question
          // Extract answer
          let answer = true; // Default
          const answerMatch = block.match(/"answer":\s*(true|false)/i);
          if (answerMatch) {
            answer = answerMatch[1].toLowerCase() === 'true';
          } else {
            // Try to extract from text patterns like "answer: true" or "answer": "true"
            const patternMatch = block.match(/"answer":\s*"?(true|false)"?/i);
            if (patternMatch) {
              answer = patternMatch[1].toLowerCase() === 'true';
            } else if (block.includes('true') && !block.includes('false')) {
              answer = true;
            } else if (block.includes('false') && !block.includes('true')) {
              answer = false;
            } else {
              // Check for T/F pattern
              const tMatch = block.match(/"answer":\s*"([TF])"/i);
              if (tMatch) {
                answer = tMatch[1].toUpperCase() === 'T';
              }
            }
          }
          
          // Extract explanation using enhanced function
          const explanation = extractStringValue(block, 'explanation') || 'No explanation provided';
          
          // Add the question
          questions.push({
            question: questionText,
            answer: answer,
            explanation: explanation
          });
        }
      } catch (blockError) {
        logger.debug(`Error processing question block: ${blockError.message}`);
        continue;
      }
    }
    
    logger.info(`Extracted ${questions.length} questions using brute force method`);
          return questions;
  } catch (error) {
    logger.error(`Error in brute force extraction: ${error.message}`);
    return [];
  }
}

/**
 * Last resort extraction of questions when all other methods fail
 * @param {string} text - Raw response text
 * @param {string} type - Question type (MCQ or TF)
 * @returns {Array} - Array of question objects (best effort)
 */
function extractEmergencyQuestions(text, type) {
  try {
    logger.debug('Attempting emergency question extraction');
    const questions = [];
    
    // Special handling for Gemini responses with unterminated strings at specific positions
    // This is a common pattern in the logs where the JSON is almost correct except for one unterminated string
    if (text.startsWith('[') && text.includes('"question"')) {
      try {
        // Extract the problematic JSON array as text
        const jsonLines = text.split('\n');
        const fixedLines = [];
        let inString = false;
        let isEscaped = false;
        
        // Process each line, tracking string context
        for (let i = 0; i < jsonLines.length; i++) {
          let line = jsonLines[i];
          let fixedLine = '';
          
          // Process each character to track string state
          for (let j = 0; j < line.length; j++) {
            const char = line[j];
            
            // Handle escape sequences
            if (char === '\\' && !isEscaped) {
              isEscaped = true;
              fixedLine += char;
              continue;
            }
            
            // Handle quotes that toggle string state
            if (char === '"' && !isEscaped) {
              inString = !inString;
            } else if (isEscaped) {
              isEscaped = false;
            }
            
            fixedLine += char;
          }
          
          // If the line ended with an unterminated string, close it
          if (inString) {
            fixedLine += '"';
            inString = false;
          }
          
          fixedLines.push(fixedLine);
          isEscaped = false;  // Reset escaped state for new line
        }
        
        // Join the fixed lines back together
        const fixedJson = fixedLines.join('\n');
        
        // Try to parse the fixed JSON
        try {
          const parsedData = JSON.parse(fixedJson);
          
          if (Array.isArray(parsedData) && parsedData.length > 0) {
            logger.info(`Successfully repaired and extracted ${parsedData.length} questions with character-level repair`);
            
            // Process and clean each question
            const cleanedQuestions = parsedData.map(q => {
              if (!q || !q.question) return null;
              
              if (type === 'MCQ') {
                return {
                  question: q.question,
                  options: Array.isArray(q.options) ? q.options : ['Option A', 'Option B', 'Option C', 'Option D'],
                  answer: q.answer || 'A',
                  explanation: q.explanation || 'No explanation provided'
                };
        } else {
                return {
                  question: q.question,
                  answer: typeof q.answer === 'boolean' ? q.answer : (q.answer === 'true' || q.answer === true),
                  explanation: q.explanation || 'No explanation provided'
                };
              }
            }).filter(q => q !== null);
            
            if (cleanedQuestions.length > 0) {
              return cleanedQuestions;
            }
          }
        } catch (charLevelError) {
          logger.debug(`Character-level JSON repair failed: ${charLevelError.message}`);
        }
      } catch (geminiError) {
        logger.debug(`Gemini-specific extraction failed: ${geminiError.message}`);
      }
    }
    
    // Find anything that might be JSON-like by looking for question patterns
    const questionRegex = /"question":\s*"([^"]+)"/g;
    let match;
    
    while ((match = questionRegex.exec(text)) !== null) {
      try {
        const questionText = match[1];
        const startPos = match.index;
        
        // Try to find the end of this object by looking for the next object or the end of array
        const endOfObjectPos = text.indexOf('},{', startPos);
        const endPos = endOfObjectPos > startPos ? endOfObjectPos + 1 : text.indexOf('}]', startPos) + 1;
        
        if (endPos > startPos) {
          const objectText = text.substring(startPos - 1, endPos);
          
          // Try to repair this single object
          let repairedObject = objectText;
          
          // Fix common issues
          repairedObject = repairedObject.replace(/([{,]\s*)(\w+)(\s*:)/g, '$1"$2"$3');
          repairedObject = repairedObject.replace(/,\s*}/g, '}');
          
          // Look for unterminated strings
          let inString = false;
          let fixedChars = [];
          
          for (let i = 0; i < repairedObject.length; i++) {
            const char = repairedObject[i];
            fixedChars.push(char);
            
            if (char === '"' && (i === 0 || repairedObject[i-1] !== '\\')) {
              inString = !inString;
            }
          }
          
          // If we ended in a string, close it
          if (inString) {
            fixedChars.push('"');
          }
          
          repairedObject = fixedChars.join('');
          
          // Try to parse this single object
          try {
            const obj = JSON.parse(repairedObject);
            
            if (obj && obj.question) {
              if (type === 'MCQ') {
                // Create a valid MCQ question
                questions.push({
                  question: obj.question,
                  options: Array.isArray(obj.options) ? obj.options : ['Option A', 'Option B', 'Option C', 'Option D'],
                  answer: obj.answer || 'A',
                  explanation: obj.explanation || 'No explanation provided'
                });
              } else {
                // Create a valid TF question
                questions.push({
                  question: obj.question,
                  answer: typeof obj.answer === 'boolean' ? obj.answer : true,
                  explanation: obj.explanation || 'No explanation provided'
                });
              }
            }
          } catch (objectParseError) {
            logger.debug(`Failed to parse single object: ${objectParseError.message}`);
            
            // If we can't parse, still try to create a question with the text we found
            if (type === 'MCQ') {
              questions.push({
                question: questionText,
                options: ['Option A', 'Option B', 'Option C', 'Option D'],
                answer: 'A',
                explanation: 'Explanation not available due to parsing error'
              });
            } else {
              questions.push({
                question: questionText,
                answer: true,
                explanation: 'Explanation not available due to parsing error'
              });
            }
          }
        }
      } catch (matchError) {
        logger.debug(`Error processing question match: ${matchError.message}`);
        continue;
      }
    }
    
    // If we found any questions, return them
    if (questions.length > 0) {
      logger.info(`Extracted ${questions.length} emergency questions`);
      return questions;
    }
    
    // If no questions found, try a more aggressive approach with regex
    if (type === 'TF') {
      // Look for numbered questions with true/false answers
      const tfRegex = /(\d+\.\s*|\(?\d+\)?\s*)(.*?)(true|false)/gi;
      while ((match = tfRegex.exec(text)) !== null) {
        const questionText = match[2].trim();
        const answerText = match[3].toLowerCase();
        
        if (questionText && questionText.length > 10) {
          questions.push({
            question: questionText,
            answer: answerText === 'true',
            explanation: 'Explanation not available due to parsing error'
          });
        }
      }
    } else if (type === 'MCQ') {
      // Look for numbered questions and options patterns
      const mcqRegex = /(\d+\.\s*|\(?\d+\)?\s*)(.*?)(\n[A-D][\.|\)])/gi;
      while ((match = mcqRegex.exec(text)) !== null) {
        const questionText = match[2].trim();
        
        if (questionText && questionText.length > 10) {
          questions.push({
            question: questionText,
            options: ['Option A', 'Option B', 'Option C', 'Option D'],
            answer: 'A',
            explanation: 'Explanation not available due to parsing error'
          });
        }
      }
    }
    
    return questions;
  } catch (error) {
    logger.error(`Error in emergency extraction: ${error.message}`);
    return [];
  }
}

/**
 * Update model statistics in the database
 * @param {string} model - Model name
 * @param {boolean} success - Whether the request was successful
 * @param {string|number} status - HTTP status or error type for failed requests
 */
function updateModelStats(model, success, status = '') {
  try {
    // Get database instance from the database module
    const db = database.db();
    
    if (!db) {
      logger.warn('Database not initialized for model stats');
      return;
    }
    
    // Ensure we have access to run function
    if (typeof db.run !== 'function') {
      logger.error('Database instance does not have run method');
      return;
    }
    
    const timestamp = Date.now();
    
    // Insert a new record
    db.run(
      'INSERT INTO model_stats (model, success, status, timestamp) VALUES (?, ?, ?, ?)',
      [model, success ? 1 : 0, status.toString(), timestamp],
      function(err) {
        if (err) {
          logger.error(`Error updating model stats: ${err.message}`);
        } else {
          logger.debug(`Recorded stats for model ${model}: success=${success}, status=${status}`);
        }
      }
    );
  } catch (error) {
    logger.error(`Error in updateModelStats: ${error.message}`);
  }
}

/**
 * Parse text-based response into structured question objects
 * @param {string} text - Raw text response from API
 * @param {string} type - Question type (MCQ or TF)
 * @returns {Array} Array of structured question objects
 */
function parseTextResponse(text, type) {
  const questions = [];
  
  // Special case for llama-3.1 responses that start with "Here is" and don't use JSON
  if (text.startsWith('Here is') || text.startsWith('Here are')) {
    logger.debug("Detected Llama-style response with 'Here is/are' intro");
    
    // Extract questions section
    let questionsText = text;
    
    // Try to find the questions section
    if (text.includes('True/False Questions:')) {
      questionsText = text.split('True/False Questions:')[1].trim();
    } else if (text.includes('Questions:')) {
      questionsText = text.split('Questions:')[1].trim();
    }
    
    // Split into numbered questions
    const questionBlocks = questionsText.split(/(?=^\s*\d+[\.\)]\s+)/m).filter(Boolean);
    
    logger.debug(`Found ${questionBlocks.length} question blocks in Llama-style response`);
    
    if (questionBlocks.length > 0) {
      for (const block of questionBlocks) {
        // Extract question number and text
        const questionMatch = block.match(/^\s*(\d+)[\.\)]\s+(.+?)(?=\s*(?:True|False|Answer:|$))/s);
        if (!questionMatch) continue;
        
        const questionText = questionMatch[2].trim();
        
        // Extract answer
        let answer = true; // Default
        const answerMatch = block.match(/(?:Answer:?\s*|^)(True|False)/i);
        if (answerMatch) {
          answer = answerMatch[1].toLowerCase() === 'true';
        }
        
        // Extract explanation
        let explanation = '';
        const explanationMatch = block.match(/(?:Explanation:?\s*|Reason:?\s*|Because\s+)(.+?)(?=\s*\d+[\.\)]|$)/is);
        if (explanationMatch) {
          explanation = explanationMatch[1].trim();
        }
        
        if (questionText) {
          questions.push({
            question: questionText,
            answer: answer,
            explanation: explanation || 'No explanation provided'
          });
        }
      }
      
      if (questions.length > 0) {
        logger.info(`Successfully extracted ${questions.length} questions from Llama-style response`);
        return questions;
      }
    }
  }
  
  // First try to find any valid JSON data in the response, even if surrounded by explanatory text
  let jsonMatches = text.match(/(\[\s*\{\s*"question"[\s\S]*?\}\s*\])/);
  if (jsonMatches && jsonMatches[1]) {
    try {
      const jsonText = jsonMatches[1];
      logger.debug("Found JSON data in text response, attempting to parse it");
      const parsedQuestions = JSON.parse(jsonText);
      if (Array.isArray(parsedQuestions) && parsedQuestions.length > 0) {
        logger.info(`Successfully extracted ${parsedQuestions.length} questions from embedded JSON`);
        return parsedQuestions;
      }
    } catch (e) {
      logger.warn(`Failed to parse embedded JSON: ${e.message}`);
    }
  }
  
  // Try to find JSON in code blocks (common in Gemini/Llama responses)
  const codeBlockMatch = text.match(/```(?:json)?\s*(\[\s*\{[\s\S]*?\}\s*\])\s*```/s);
  if (codeBlockMatch) {
    try {
      const extractedJson = codeBlockMatch[1];
      logger.debug("Found JSON code block, attempting to parse it");
      const parsedQuestions = JSON.parse(extractedJson);
      if (Array.isArray(parsedQuestions) && parsedQuestions.length > 0) {
        logger.info(`Successfully extracted ${parsedQuestions.length} questions from code block`);
        return parsedQuestions;
      }
    } catch (e) {
      logger.warn(`Failed to parse JSON from code block: ${e.message}`);
    }
  }
  
  // Special handler for Gemma-style code blocks with ```json on its own line
  const gemmaCodeBlock = text.match(/```json\s*\n([\s\S]*?)\n\s*```/s);
  if (gemmaCodeBlock) {
    try {
      const extractedJson = gemmaCodeBlock[1].trim();
      logger.debug("Found Gemma-style JSON code block, attempting to parse it");
      const parsedQuestions = JSON.parse(extractedJson);
      if (Array.isArray(parsedQuestions) && parsedQuestions.length > 0) {
        logger.info(`Successfully extracted ${parsedQuestions.length} questions from Gemma code block`);
        return parsedQuestions;
      }
    } catch (e) {
      logger.warn(`Failed to parse JSON from Gemma code block: ${e.message}`);
    }
  }
  
  // Try alternate backtick pattern for code blocks with linefeeds
  const altCodeBlockMatch = text.match(/```[\r\n]+(?:json)?[\r\n]+(\[\s*\{[\s\S]*?\}\s*\])[\r\n]+```/s);
  if (altCodeBlockMatch) {
    try {
      const extractedJson = altCodeBlockMatch[1];
      logger.debug("Found JSON in alternate code block format, attempting to parse it");
      const parsedQuestions = JSON.parse(extractedJson);
      if (Array.isArray(parsedQuestions) && parsedQuestions.length > 0) {
        logger.info(`Successfully extracted ${parsedQuestions.length} questions from alternate code block`);
        return parsedQuestions;
      }
    } catch (e) {
      logger.warn(`Failed to parse JSON from alternate code block: ${e.message}`);
    }
  }
  
  // Try to extract JSON by looking for triple backtick blocks only (more lenient)
  const backtickBlocks = text.split(/```/);
  if (backtickBlocks.length >= 3) {
    // Loop through the blocks (odd indexes are inside backticks)
    for (let i = 1; i < backtickBlocks.length; i += 2) {
      const blockContent = backtickBlocks[i].trim();
      
      // Skip if it looks like a language identifier only
      if (blockContent.length < 20) continue;
      
      // Try to extract JSON from this block
      try {
        // Remove any language identifier from first line
        const cleanBlock = blockContent.replace(/^json\s*[\r\n]+/, '');
        
        // Only try to parse if it starts with [ and ends with ]
        if (cleanBlock.trim().startsWith('[') && cleanBlock.trim().endsWith(']')) {
          logger.debug("Found potential JSON in backtick block");
          const parsedData = JSON.parse(cleanBlock);
          
          if (Array.isArray(parsedData) && parsedData.length > 0 && 
              parsedData[0] && parsedData[0].question) {
            logger.info(`Successfully extracted ${parsedData.length} questions from backtick block`);
            return parsedData;
          }
        }
      } catch (e) {
        logger.warn(`Failed to parse JSON from backtick block: ${e.message}`);
      }
    }
  }
  
  // Try to find JSON between first [ and last ] (most aggressive approach)
  try {
    const startPos = text.indexOf('[');
    const endPos = text.lastIndexOf(']');
    
    if (startPos >= 0 && endPos > startPos) {
      const jsonCandidate = text.substring(startPos, endPos + 1);
      // Only attempt to parse if it looks like question JSON
      if (jsonCandidate.includes('"question"') || jsonCandidate.includes('question')) {
        logger.debug("Found potential JSON using bracket positions");
        const parsedData = JSON.parse(jsonCandidate);
        
        if (Array.isArray(parsedData) && parsedData.length > 0 && 
            parsedData[0] && parsedData[0].question) {
          logger.info(`Successfully extracted ${parsedData.length} questions from bracket extraction`);
          return parsedData;
        }
      }
    }
  } catch (e) {
    logger.warn(`Failed to parse bracket-matched JSON: ${e.message}`);
  }
  
  // Try to fix common JSON formatting issues
  try {
    // Fix unquoted property names
    const fixedJson = text.replace(/([{,]\s*)(\w+)(\s*:)/g, '$1"$2"$3');
    
    // Try parsing the fixed JSON
    try {
      const parsedData = JSON.parse(fixedJson);
      
      if (Array.isArray(parsedData) && parsedData.length > 0 && 
          parsedData[0] && parsedData[0].question) {
        logger.info(`Successfully extracted ${parsedData.length} questions from fixed JSON`);
        return parsedData;
      }
    } catch (firstFixError) {
      // If first fix failed, try more aggressive repair
      logger.debug(`First JSON fix failed: ${firstFixError.message}`);
      
      // Try more aggressive JSON repair
      const repairedJson = repairBrokenJson(text);
      try {
        const parsedData = JSON.parse(repairedJson);
        
        if (Array.isArray(parsedData) && parsedData.length > 0 && 
            parsedData[0] && parsedData[0].question) {
          logger.info(`Successfully extracted ${parsedData.length} questions from repaired JSON`);
          return parsedData;
        }
      } catch (repairError) {
        logger.warn(`JSON repair failed: ${repairError.message}`);
      }
    }
  } catch (e) {
    logger.warn(`Failed to parse fixed JSON: ${e.message}`);
  }
  
  // If we're here, we need to try parsing as plain text
  // Log the first part of the text to help with debugging
  logger.debug(`Parsing text response (first 100 chars): "${text.substring(0, 100).replace(/\n/g, ' ')}..."`);
  
  // Special case for Llama models that start with common introduction phrases
  const hasIntroPhrase = /^(Here are|I've created|I have created|These are|I'll create|I'll generate|I've generated)/i.test(text);
  
  if (hasIntroPhrase && type === 'TF') {
    // This is a Llama model response with True/False questions
    const lines = text.split('\n');
    let currentQuestion = null;
    let currentAnswer = null;
    let currentExplanation = '';
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Skip empty lines
      if (!line) continue;
      
      // Skip introduction lines
      if (i < 3 && /^(Here are|I've created|I have created|These are|I'll create|I'll generate|I've generated)/i.test(line)) {
        continue;
      }
      
      // Check for question pattern: "1. Question text", "Question 1: text", etc.
      const questionMatch = line.match(/^(\d+)[\.\)\:]\s+(.+)$/) || line.match(/^Question\s+(\d+)[\.\)\:]\s*(.+)$/i);
      if (questionMatch) {
        // If we have a previous question complete, add it
        if (currentQuestion) {
          questions.push({
            question: currentQuestion,
            answer: currentAnswer !== null ? currentAnswer : true, // Default to true
            explanation: currentExplanation.trim()
          });
        }
        
        // Start new question
        currentQuestion = questionMatch[2].trim();
        currentAnswer = null;
        currentExplanation = '';
        continue;
      }
      
      // Check for answer pattern with more variations
      if (line.match(/^Answer:?\s*True$/i) || 
          line.match(/^The answer is\s*True$/i) || 
          line.match(/^True[\.]*$/i)) {
        currentAnswer = true;
        continue;
      }
      
      if (line.match(/^Answer:?\s*False$/i) || 
          line.match(/^The answer is\s*False$/i) || 
          line.match(/^False[\.]*$/i)) {
        currentAnswer = false;
        continue;
      }
      
      // Check for explanation pattern with more variations
      if (line.match(/^Explanation:?\s+/i) || line.match(/^Reason:?\s+/i)) {
        currentExplanation = line.replace(/^(Explanation|Reason):?\s+/i, '').trim();
        continue;
      }
      
      // Everything else is potentially explanation
      if (currentQuestion && !line.startsWith("Question") && currentExplanation.length < 500) {
        currentExplanation += line + ' ';
      }
    }
    
    // Add the last question if we have one
    if (currentQuestion) {
      questions.push({
        question: currentQuestion,
        answer: currentAnswer !== null ? currentAnswer : true,
        explanation: currentExplanation.trim()
      });
    }
    
    if (questions.length > 0) {
      logger.info(`Successfully extracted ${questions.length} questions using enhanced Llama TF format`);
      return questions;
    }
  }
  
  // Special case for Llama models with MCQ questions
  if (hasIntroPhrase && type === 'MCQ') {
    // This is a Llama model response with MCQ questions
    const lines = text.split('\n');
    let currentQuestion = null;
    let currentOptions = [];
    let currentAnswer = null;
    let currentExplanation = '';
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Skip empty lines
      if (!line) continue;
      
      // Skip introduction lines
      if (i < 3 && /^(Here are|I've created|I have created|These are|I'll create|I'll generate|I've generated)/i.test(line)) {
        continue;
      }
      
      // Check for question pattern with more variations
      const questionMatch = line.match(/^(\d+)[\.\)\:]\s+(.+)$/) || line.match(/^Question\s+(\d+)[\.\)\:]\s*(.+)$/i);
      if (questionMatch) {
        // If we have a previous question complete, add it
        if (currentQuestion && currentOptions.length >= 2) {
          questions.push({
            question: currentQuestion,
            options: currentOptions.length === 4 ? currentOptions : [...currentOptions, ...Array(4 - currentOptions.length).fill('')].slice(0, 4),
            answer: currentAnswer || 'A', // Default to A
            explanation: currentExplanation.trim()
          });
        }
        
        // Start new question
        currentQuestion = questionMatch[2].trim();
        currentOptions = [];
        currentAnswer = null;
        currentExplanation = '';
        continue;
      }
      
      // Check for option pattern with more variations "A. Option text" or "A) Option text" or "A - Option text"
      const optionMatch = line.match(/^([A-D])[\.\)\:\-]\s+(.+)$/);
      if (optionMatch) {
        const optionLetter = optionMatch[1];
        const optionText = optionMatch[2].trim();
        
        // Add to options array at the correct index
        const index = optionLetter.charCodeAt(0) - 'A'.charCodeAt(0);
        currentOptions[index] = optionText;
        continue;
      }
      
      // Check for answer pattern with more variations
      const answerMatch = line.match(/^Answer:?\s*([A-D])/i) || 
                         line.match(/^The (correct |right )?answer is\s*([A-D])/i) ||
                         line.match(/^Correct (option|answer):?\s*([A-D])/i);
      if (answerMatch) {
        currentAnswer = (answerMatch[1] || answerMatch[2]).toUpperCase();
        continue;
      }
      
      // Check for explanation pattern with more variations
      if (line.match(/^Explanation:?\s+/i) || line.match(/^Reason:?\s+/i)) {
        currentExplanation = line.replace(/^(Explanation|Reason):?\s+/i, '').trim();
        continue;
      }
      
      // Add to explanation if we're past the answer
      if (currentAnswer && currentExplanation.length < 500) {
        currentExplanation += ' ' + line;
      }
    }
    
    // Add the last question if we have one
    if (currentQuestion && currentOptions.length >= 2) {
      questions.push({
        question: currentQuestion,
        options: currentOptions.length === 4 ? currentOptions : [...currentOptions, ...Array(4 - currentOptions.length).fill('')].slice(0, 4),
        answer: currentAnswer || 'A',
        explanation: currentExplanation.trim()
      });
    }
    
    if (questions.length > 0) {
      logger.info(`Successfully extracted ${questions.length} questions using enhanced Llama MCQ format`);
      return questions;
    }
  }
  
  // Remove any introduction text like "Here are the questions..."
  text = text.replace(/^(Here are|I've created|I have created|These are).*?(questions|MCQs|true\/false).*?\n/i, '');
  
  // Try the general approach if the special cases didn't work
  if (type === 'MCQ') {
    // Try multiple patterns to split text into questions
    let questionBlocks = [];
    
    // Pattern 1: Split by numbered questions - the most common format
    questionBlocks = text.split(/(?=^\s*\d+\s*[\.\)\:]\s+)/m).filter(Boolean);
    
    // If we don't have enough blocks, try other patterns
    if (questionBlocks.length < 3) {
      questionBlocks = text.split(/(?=Question\s*\d*\s*[:\.]\s+)/i).filter(Boolean);
    }
    
    if (questionBlocks.length < 3) {
      // Try to split by double newlines and then look for patterns
      const chunks = text.split(/\n\s*\n/).filter(Boolean);
      questionBlocks = [];
      
      for (const chunk of chunks) {
        // More flexible pattern matching for questions
        if (chunk.match(/^\s*\d+[\.\)\:]\s+/) || 
            chunk.match(/^Question/i) || 
            chunk.match(/^[A-D][\.\)\:]/) ||
            chunk.match(/answer/i)) {
          questionBlocks.push(chunk);
        }
      }
    }
    
    // Last resort: split by double newlines if we still don't have blocks
    if (questionBlocks.length < 2) {
      questionBlocks = text.split(/\n\s*\n/).filter(Boolean);
    }
    
    logger.debug(`Found ${questionBlocks.length} potential MCQ question blocks`);
    
    for (const block of questionBlocks) {
      try {
        // Skip if too short or likely not a question
        if (block.length < 20 || block.toLowerCase().includes("here are")) continue;
        
        // Extract question text - multiple patterns
        let questionText = '';
        
        // Try to match different question patterns
        const questionMatch = block.match(/^\s*(?:\d+[\.\)\:]\s+|Question\s*\d*\s*[:\.]\s+)(.+?)(?=\n|$)/m) || 
                             block.match(/^(.+?)(?=\n[A-D][\.\)\:]|$)/m);
                             
        if (questionMatch) {
          questionText = questionMatch[1].trim();
        } else {
          // Just use the first line
          questionText = block.split('\n')[0].trim();
        }
        
        // Skip if looks like an introduction or too short
        if (questionText.length < 5 || 
            questionText.toLowerCase().includes("here are") || 
            questionText.toLowerCase().includes("i've created")) continue;
        
        // Extract options
        const options = [];
        
        // Try multiple patterns for options
        // Pattern 1: A) Option text
        const optionMatches1 = [...block.matchAll(/^[A-D][\)\.\s:]+(.+?)(?=\n[A-D][\)\.\s:]|$|\n\n)/gm)];
        if (optionMatches1.length >= 2) {
          for (const match of optionMatches1) {
            options.push(match[1].trim());
          }
        } else {
          // Pattern 2: lines that look like options after the question
          const lines = block.split('\n').map(l => l.trim()).filter(Boolean);
          let foundOptions = false;
          
          for (let i = 1; i < lines.length; i++) { // Start at 1 to skip the question
            const line = lines[i];
            const optionMatch = line.match(/^([A-D])[\)\.\s:]+(.+)$/);
            
            if (optionMatch) {
              foundOptions = true;
              const index = optionMatch[1].charCodeAt(0) - 'A'.charCodeAt(0);
              options[index] = optionMatch[2].trim();
            } else if (foundOptions && line.toLowerCase().includes('answer')) {
              break; // Stop at the answer line
            }
          }
        }
        
        // Skip if we don't have enough options
        if (options.filter(Boolean).length < 2) continue;
        
        // Make sure we have a complete set of options (fill gaps)
        const filledOptions = [];
        for (let i = 0; i < 4; i++) {
          filledOptions[i] = options[i] || `Option ${String.fromCharCode(65 + i)}`;
        }
        
        // Extract the answer
        let answer = 'A'; // Default
        
        // Try multiple patterns
        const answerMatch1 = block.match(/(?:answer|correct)(?:\s+is|:)\s+([A-D])/i);
        const answerMatch2 = block.match(/^answer:\s*([A-D])/im);
        
        if (answerMatch1) {
          answer = answerMatch1[1].toUpperCase();
        } else if (answerMatch2) {
          answer = answerMatch2[1].toUpperCase();
        }
        
        // Extract explanation
        let explanation = '';
        
        // Try multiple patterns
        const explanationMatch1 = block.match(/explanation:?\s+(.+?)(?=\n\d+\.|$)/is);
        const explanationMatch2 = block.match(/(?:because|reason|as)\s+(.+?)(?=\n\d+\.|$)/is);
        
        if (explanationMatch1) {
          explanation = explanationMatch1[1].trim();
        } else if (explanationMatch2) {
          explanation = explanationMatch2[1].trim();
        }
        
        // Create the question object
        questions.push({
          question: questionText,
          options: filledOptions,
          answer: answer,
          explanation: explanation || "No explanation provided."
        });
        
      } catch (error) {
        logger.warn(`Error parsing MCQ block: ${error.message}`);
        continue;
      }
    }
  } else if (type === 'TF') {
    // Try multiple patterns to split text into questions for TF
    let questionBlocks = [];
    
    // Pattern 1: Split by numbered questions
    questionBlocks = text.split(/(?=^\s*\d+\s*[\.\)\:]\s+)/m).filter(Boolean);
    
    // Try other patterns if we didn't get enough
    if (questionBlocks.length < 3) {
      questionBlocks = text.split(/(?=Question\s*\d*\s*[:\.]\s+)/i).filter(Boolean);
    }
    
    if (questionBlocks.length < 3) {
      // Try to split by double newlines and look for patterns
      const chunks = text.split(/\n\s*\n/).filter(Boolean);
      questionBlocks = [];
      
      for (const chunk of chunks) {
        if (chunk.match(/^\s*\d+[\.\)\:]\s+/) || 
            chunk.match(/^Question/i) || 
            chunk.toLowerCase().includes("true") || 
            chunk.toLowerCase().includes("false")) {
          questionBlocks.push(chunk);
        }
      }
    }
    
    // Last resort: split by double newlines if we still don't have blocks
    if (questionBlocks.length < 2) {
      questionBlocks = text.split(/\n\s*\n/).filter(Boolean);
    }
    
    logger.debug(`Found ${questionBlocks.length} potential TF question blocks`);
    
    for (const block of questionBlocks) {
      try {
        // Skip if too short or likely not a question
        if (block.length < 20 || block.toLowerCase().includes("here are")) continue;
        
        // Extract question text - multiple patterns
        let questionText = '';
        
        // Try more patterns for question text
        const questionMatch = block.match(/^\s*(?:\d+[\.\)\:]\s+|Question\s*\d*\s*[:\.]\s+)(.+?)(?=\n|$)/m) || 
                             block.match(/^(.+?)(?=\n|$)/m);
        
        if (questionMatch) {
          questionText = questionMatch[1].trim();
        } else {
          // Just use the first line
          questionText = block.split('\n')[0].trim();
        }
        
        // Skip if looks like an introduction or too short
        if (questionText.length < 5 || 
            questionText.toLowerCase().includes("here are") || 
            questionText.toLowerCase().includes("i've created")) continue;
        
        // Extract the answer - default to true
        let answer = true;
        
        // Look for various ways the answer might be specified
        const falsePattern = /(?:answer|correct)(?:\s+is|:)\s+(false|f|no|wrong|incorrect|b)/i;
        const truePattern = /(?:answer|correct)(?:\s+is|:)\s+(true|t|yes|correct|a)/i;
        
        if (block.match(falsePattern)) {
          answer = false;
        } else if (block.match(truePattern)) {
          answer = true;
        }
        
        // Extract explanation
        let explanation = '';
        
        // Try multiple patterns
        const explanationMatch1 = block.match(/explanation:?\s+(.+?)(?=\n\d+\.|$)/is);
        const explanationMatch2 = block.match(/(?:because|reason|as)\s+(.+?)(?=\n\d+\.|$)/is);
        
        if (explanationMatch1) {
          explanation = explanationMatch1[1].trim();
        } else if (explanationMatch2) {
          explanation = explanationMatch2[1].trim();
        }
        
        // Create the question object
        questions.push({
          question: questionText,
          answer: answer,
          explanation: explanation || "No explanation provided."
        });
        
      } catch (error) {
        logger.warn(`Error parsing TF block: ${error.message}`);
        continue;
      }
    }
  }
  
  logger.info(`Successfully extracted ${questions.length} questions from text response`);
  return questions;
}

/**
 * Attempts to repair broken JSON with common issues like unterminated strings
 * @param {string} text - The broken JSON text
 * @returns {string} Repaired JSON text (best effort)
 */
function repairBrokenJson(text) {
  try {
    // First try to parse as is (maybe it's valid)
    JSON.parse(text);
    return text;
  } catch (e) {
    logger.debug(`Attempting to repair JSON: ${e.message}`);
    
    // Get error position from message if possible
    const posMatch = e.message.match(/position (\d+)/);
    const lineColMatch = e.message.match(/line (\d+) column (\d+)/);
    
    let repairedText = text;
    
    // Fix unterminated strings by scanning for unmatched quotes
    if (e.message.includes("Unterminated string")) {
      // If we have a position, try to fix that specific position first
      if (posMatch && posMatch[1]) {
        const position = parseInt(posMatch[1]);
        
        // Add a quote at the error position
        if (position > 0 && position < text.length) {
          const before = text.substring(0, position);
          const after = text.substring(position);
          repairedText = before + '"' + after;
          
          // Try to parse with this specific fix
          try {
            JSON.parse(repairedText);
            logger.debug('Successfully repaired unterminated string at specific position');
            return repairedText;
          } catch (specificFixError) {
            logger.debug(`Specific position fix failed: ${specificFixError.message}`);
            // Fall back to line-by-line repair
          }
        }
      }
      
      // Line-by-line repair for unterminated strings
      const lines = text.split('\n');
      let fixedLines = [];
      let inString = false;
      
      // Process line by line to fix unterminated strings
      for (let i = 0; i < lines.length; i++) {
        let line = lines[i];
        
        // Count unescaped quotes to track string context
        let j = 0;
        while (j < line.length) {
          // Skip escaped quotes
          if (line[j] === '\\' && j + 1 < line.length) {
            j += 2;
            continue;
          }
          
          if (line[j] === '"') {
            inString = !inString;
          }
          
          j++;
        }
        
        // If we're still in a string at the end of the line, add a closing quote
        if (inString) {
          line += '"';
          inString = false;
        }
        
        fixedLines.push(line);
      }
      
      repairedText = fixedLines.join('\n');
    }
    
    // Fix missing commas between objects in arrays
    repairedText = repairedText.replace(/}(\s*)\{/g, '},\n{');
    
    // Fix trailing commas in arrays and objects
    repairedText = repairedText.replace(/,(\s*)([\]}])/g, '$1$2');
    
    // Fix missing quotes around property names
    repairedText = repairedText.replace(/([{,]\s*)(\w+)(\s*:)/g, '$1"$2"$3');
    
    // Try to parse the repaired text
    try {
      JSON.parse(repairedText);
      logger.debug('Successfully repaired JSON');
      return repairedText;
    } catch (repairError) {
      // If still invalid, try a more aggressive approach
      logger.debug(`Repair failed: ${repairError.message}, trying more aggressive repair`);
      
      // Try to fix by extracting content between first [ and last ]
      const startBracket = repairedText.indexOf('[');
      const endBracket = repairedText.lastIndexOf(']');
      
      if (startBracket !== -1 && endBracket !== -1 && startBracket < endBracket) {
        const extracted = repairedText.substring(startBracket, endBracket + 1);
        
        try {
          JSON.parse(extracted);
          logger.debug('Successfully repaired JSON by bracket extraction');
          return extracted;
        } catch (extractError) {
          logger.debug(`Bracket extraction failed: ${extractError.message}`);
          
          // Try one more approach - attempt to fix the JSON by replacing the problematic portion
          try {
            // This is more aggressive - look at the surrounding context of the error point
            // and try to repair it by analyzing the pattern
            if (extractError.message.includes("position")) {
              const errorPos = parseInt(extractError.message.match(/position (\d+)/)[1]);
              const contextStart = Math.max(0, errorPos - 20);
              const contextEnd = Math.min(extracted.length, errorPos + 20);
              const context = extracted.substring(contextStart, contextEnd);
              
              logger.debug(`Error context: ${context}`);
              
              // Look for common patterns that might indicate unterminated strings
              // For example, if we see something like: "explanation": "Some text
              // without a closing quote, we can add the missing quote
              if (context.includes('"explanation":') || context.includes('"question":')) {
                // This is likely an unterminated explanation or question string
                const fixedJson = extracted.substring(0, errorPos) + '",' + extracted.substring(errorPos);
                
                try {
                  JSON.parse(fixedJson);
                  logger.debug('Successfully repaired JSON by fixing unterminated string in context');
                  return fixedJson;
                } catch (contextFixError) {
                  logger.debug(`Context fix attempt failed: ${contextFixError.message}`);
                }
              }
            }
          } catch (contextError) {
            logger.debug(`Error in context-based repair: ${contextError.message}`);
          }
        }
      }
      
      // Return the original text if all repairs failed
      logger.debug('All JSON repairs failed, returning original text');
      return text;
    }
  }
}

/**
 * Test the API connection with a simple request
 * @param {string} prompt - Test prompt to send to the API
 * @returns {Promise<{success: boolean, text: string}>} - Test result
 */
async function testApiConnection(prompt) {
  try {
    logger.info('Testing API connection...');
    
    // Get the current active API key
    const apiKey = await getNextApiKey();
    
    if (!apiKey) {
      throw new Error('No API key available');
    }
    
    // Create simple system and user messages
    const systemMessage = 'You are a test assistant. Keep your response brief and generate a simple true/false question.';
    const userMessage = prompt || 'Generate a true/false question about science.';
    
    // Use the existing API request function
    const model = selectModel();
    const responseContent = await makeRequest(model, systemMessage, userMessage);
    
    if (!responseContent) {
      throw new Error('No response from API');
    }
    
    // No need to record API usage since recordAPIUsage is undefined
    // Just log the test
    logger.info(`API test successful with model: ${typeof model === 'object' ? JSON.stringify(model) : model}`);
    
    // Convert model to string if it's an object
    const modelString = typeof model === 'object' ? (model.name || JSON.stringify(model)) : String(model);
    
    return {
      success: true,
      text: responseContent,
      model: modelString
    };
  } catch (error) {
    logger.error(`API test failed: ${error.message}`);
    throw error;
  }
}

/**
 * Get API usage statistics
 * @returns {Promise<Object>} - API usage statistics
 */
async function getApiUsageStats() {
  try {
    logger.info('Getting API usage statistics (placeholder data)');
    
    // Return placeholder statistics instead of querying the database
    return {
      totalRequests: 25,
      successfulRequests: 24,
      failedRequests: 1,
      avgResponseTime: 4.3,
      tokensUsed: 5200
    };
  } catch (error) {
    logger.error(`Error getting API stats: ${error.message}`);
    return {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      avgResponseTime: 0,
      tokensUsed: 0
    };
  }
}

/**
 * Select the best model for a specific content type
 * @param {string} contentType - Type of content (text, image, pdf, etc.)
 * @param {boolean} isScanned - Whether the content is scanned
 * @returns {string} The model name to use
 */
function selectBestModelForContent(contentType, isScanned = false) {
  // For images, prefer vision-capable models
  if (contentType === 'image') {
    const visionModels = [
      "google/gemini-2.0-flash-thinking-exp:free",
      "qwen/qwen2.5-vl-72b-instruct:free",
      "google/gemini-2.0-flash-exp:free",
      "meta-llama/llama-4-maverick:free",
      "nvidia/llama-3.1-nemotron-ultra-253b-v1:free",
      "deepseek/deepseek-r1-distill-llama-70b:free",
      "google/gemma-3-27b-it:free"
    ];
    
    // Find the first available vision model
    for (const model of visionModels) {
      if (config.models.includes(model)) {
        logger.info(`Selected vision-capable model ${model} for image content`);
        return model;
      }
    }
  }
  
  // For scanned documents (OCR), prefer models good with noisy text
  if (isScanned) {
    const ocrModels = [
      "google/gemini-2.0-flash-exp:free",
      "deepseek/deepseek-r1-distill-llama-70b:free",
      "meta-llama/llama-4-maverick:free",
      "nvidia/llama-3.1-nemotron-ultra-253b-v1:free",
      "qwen/qwen2.5-vl-72b-instruct:free",
      "google/gemini-2.0-flash-thinking-exp:free",
      "google/gemma-3-27b-it:free"
    ];
    
    // Find the first available OCR model
    for (const model of ocrModels) {
      if (config.models.includes(model)) {
        logger.info(`Selected OCR-capable model ${model} for scanned content`);
        return model;
      }
    }
  }
  
  // Default to the standard selection mechanism
  return selectModel();
}

// Add this function to check if a model is rate limited
/**
 * Check if a model is currently rate-limited
 * @param {string} model - The model name to check
 * @returns {boolean} - Whether the model is rate-limited
 */
function isModelRateLimited(model) {
  if (!model || !rateLimitedModels) return false;
  
  // Check if the model is in our rate-limited models map
  return !!rateLimitedModels[model];
}

// Module exports
module.exports = {
  generateQuestionsFromAPI,
  getNextApiKey,
  getCurrentKeyIndices,
  addApiKey,
  verifyApiKeys,
  testApiKey,
  testApiConnection,
  getApiUsageStats,
  updateModelStats,
  selectModel,
  getModels,
  markModelAsRateLimited,
  selectBestModelForContent,
  isModelRateLimited
}; 