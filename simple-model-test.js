const { app, BrowserWindow } = require('electron');
const path = require('path');

// Import required modules
const database = require('./src/database/database');
const IPCHandlers = require('./src/ipcHandlers');

let mainWindow;

function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'src', 'preload.js')
        },
        show: true
    });
    mainWindow.loadFile('src/renderer/index.html');
    
    // Open DevTools
    mainWindow.webContents.openDevTools();
}

async function simpleTest() {
    console.log('\n🔧 SIMPLE MODEL TEST');
    console.log('🎯 Testing IPC handler and frontend step by step\n');
    
    // Wait for the window to load
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    try {
        // Step 1: Test IPC handler directly
        console.log('📡 Step 1: Testing IPC handler directly...');
        const { ipcMain } = require('electron');
        
        // Create a mock event
        const mockEvent = {
            reply: (channel, data) => {
                console.log(`📤 IPC Reply on ${channel}:`, data);
            }
        };
        
        // Find and call the handler
        const listeners = ipcMain.listeners('get-all-models');
        console.log(`📋 Found ${listeners.length} listeners for 'get-all-models'`);
        
        if (listeners.length > 0) {
            try {
                const result = await listeners[0](mockEvent);
                console.log('✅ IPC Handler Result:', JSON.stringify(result, null, 2));
            } catch (error) {
                console.error('❌ IPC Handler Error:', error);
            }
        }
        
        // Step 2: Test from renderer
        console.log('\n📱 Step 2: Testing from renderer...');
        const rendererResult = await mainWindow.webContents.executeJavaScript(`
            (async function() {
                try {
                    console.log('🔧 Testing electronAPI...');
                    console.log('electronAPI available:', !!window.electronAPI);
                    console.log('getAllModels available:', !!window.electronAPI?.getAllModels);
                    
                    if (window.electronAPI && window.electronAPI.getAllModels) {
                        const result = await window.electronAPI.getAllModels();
                        console.log('📊 Renderer result:', result);
                        return result;
                    } else {
                        return { error: 'electronAPI not available' };
                    }
                } catch (error) {
                    console.error('❌ Renderer error:', error);
                    return { error: error.message };
                }
            })();
        `);
        
        console.log('📱 Renderer Result:', JSON.stringify(rendererResult, null, 2));
        
        // Step 3: Test manual dropdown population
        console.log('\n🎯 Step 3: Testing manual dropdown population...');
        const dropdownResult = await mainWindow.webContents.executeJavaScript(`
            (async function() {
                try {
                    const modelSelect = document.getElementById('modelSelect');
                    if (!modelSelect) {
                        return { error: 'Model select not found' };
                    }
                    
                    console.log('🔧 Found model select, current options:', modelSelect.options.length);
                    
                    // Manually add a test option
                    const testOption = document.createElement('option');
                    testOption.value = 'test-model';
                    testOption.textContent = 'Test Model';
                    modelSelect.appendChild(testOption);
                    
                    console.log('✅ Added test option, new count:', modelSelect.options.length);
                    
                    return {
                        success: true,
                        optionCount: modelSelect.options.length,
                        options: Array.from(modelSelect.options).map(opt => ({
                            value: opt.value,
                            text: opt.textContent
                        }))
                    };
                } catch (error) {
                    console.error('❌ Dropdown test error:', error);
                    return { error: error.message };
                }
            })();
        `);
        
        console.log('🎯 Dropdown Result:', JSON.stringify(dropdownResult, null, 2));
        
        // Summary
        console.log('\n📊 TEST SUMMARY:');
        console.log('═'.repeat(50));
        
        const ipcWorking = listeners.length > 0;
        const rendererWorking = rendererResult && !rendererResult.error;
        const dropdownWorking = dropdownResult && dropdownResult.success;
        
        console.log(`📡 IPC Handler: ${ipcWorking ? '✅ WORKING' : '❌ FAILED'}`);
        console.log(`📱 Renderer API: ${rendererWorking ? '✅ WORKING' : '❌ FAILED'}`);
        console.log(`🎯 Dropdown: ${dropdownWorking ? '✅ WORKING' : '❌ FAILED'}`);
        
        if (ipcWorking && rendererWorking && dropdownWorking) {
            console.log('\n🎉 ALL COMPONENTS WORKING!');
            console.log('🔧 The issue might be in the initialization timing');
        } else {
            console.log('\n❌ SOME COMPONENTS FAILED');
            console.log('🔧 Check the individual test results above');
        }
        
    } catch (error) {
        console.error('\n❌ Simple test failed:', error.message);
        console.error('Stack:', error.stack);
    }
    
    console.log('\n🔍 Check the browser console for more details.');
    console.log('Press Ctrl+C to exit when done.\n');
}

app.whenReady().then(async () => {
    try {
        // Initialize database first (like in main.js)
        console.log('🔧 Initializing database...');
        database.initDatabase();

        // Initialize IPC handlers
        console.log('🔧 Initializing IPC handlers...');
        const ipcHandlers = new IPCHandlers();
        ipcHandlers.initializeDesktopTables();

        console.log('✅ Initialization complete');

        createWindow();
        setTimeout(simpleTest, 3000);
    } catch (error) {
        console.error('❌ Initialization failed:', error);
        app.quit();
    }
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

process.on('SIGINT', () => {
    console.log('\n🛑 Simple test interrupted by user');
    app.quit();
});
