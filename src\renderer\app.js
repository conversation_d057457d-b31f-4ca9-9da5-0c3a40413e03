// Main application logic for the renderer process
class QuestionGeneratorApp {
    constructor() {
        this.currentScreen = 'welcomeScreen';
        this.selectedQuestionType = null;
        this.selectedInputMethod = null;
        this.currentQuestions = [];
        this.currentQuiz = null;
        this.quizState = {
            currentQuestionIndex: 0,
            answers: [],
            score: { correct: 0, total: 0 },
            startTime: null,
            endTime: null
        };

        // Initialize asynchronously
        this.init().catch(error => {
            console.error('Error during app initialization:', error);
        });
    }

    async init() {
        this.setupEventListeners();
        this.setupMenuListeners();
        this.setupDragAndDrop();
        await this.initializeTheme();
        await this.loadInitialSettings();
        this.showScreen('welcomeScreen');
    }

    async loadInitialSettings() {
        try {
            // Load settings to initialize app state (but don't populate UI since we're not in settings screen)
            const result = await window.electronAPI.getSettings();
            if (result.success) {
                this.appSettings = result.settings;
                console.log('Initial settings loaded:', this.appSettings);
            }
        } catch (error) {
            console.warn('Could not load initial settings:', error);
            // Set defaults
            this.appSettings = {
                questionsPerPage: 5,
                imageQuestionsCount: 15,
                shuffleQuestions: false,
                shuffleOptions: false,
                showExplanations: true,
                notifications: true,
                autoOpenPDF: true,
                theme: 'light'
            };
        }
    }

    setupEventListeners() {
        // Question type selection
        document.getElementById('mcqBtn').addEventListener('click', () => {
            this.selectQuestionType('MCQ');
        });

        document.getElementById('tfBtn').addEventListener('click', () => {
            this.selectQuestionType('TF');
        });

        // Input method selection
        document.getElementById('textInputBtn').addEventListener('click', () => {
            this.selectInputMethod('text');
        });

        document.getElementById('fileUploadBtn').addEventListener('click', () => {
            this.selectInputMethod('file');
        });

        document.getElementById('imageUploadBtn').addEventListener('click', () => {
            this.selectInputMethod('image');
        });

        // Navigation
        document.getElementById('backToWelcome').addEventListener('click', () => {
            this.showScreen('welcomeScreen');
        });

        document.getElementById('backToContent').addEventListener('click', () => {
            this.showScreen('contentScreen');
        });

        // Content generation
        document.getElementById('generateFromText').addEventListener('click', () => {
            this.generateFromText();
        });

        document.getElementById('generateFromFile').addEventListener('click', () => {
            this.generateFromFile();
        });

        document.getElementById('generateFromImage').addEventListener('click', () => {
            this.generateFromImage();
        });

        // File upload - for desktop, we'll use the file dialog instead
        document.getElementById('fileInput').addEventListener('change', async (e) => {
            if (e.target.files[0]) {
                // For desktop, trigger file selection dialog instead
                await this.generateFromFile();
            }
        });

        document.getElementById('imageInput').addEventListener('change', async (e) => {
            if (e.target.files[0]) {
                // For desktop, trigger file selection dialog instead
                await this.generateFromImage();
            }
        });

        // File removal
        document.getElementById('removeFile').addEventListener('click', () => {
            this.removeFile();
        });

        document.getElementById('removeImage').addEventListener('click', () => {
            this.removeImage();
        });

        // Quiz actions
        document.getElementById('startQuizBtn').addEventListener('click', () => {
            this.startInteractiveQuiz();
        });

        document.getElementById('exportQuestionsBtn').addEventListener('click', () => {
            this.exportQuestions();
        });

        document.getElementById('showAnswersBtn').addEventListener('click', () => {
            this.showQuestionsWithAnswers();
        });

        document.getElementById('submitAnswer').addEventListener('click', () => {
            this.submitQuizAnswer();
        });

        document.getElementById('nextQuestion').addEventListener('click', () => {
            this.nextQuizQuestion();
        });

        document.getElementById('finishQuiz').addEventListener('click', () => {
            this.finishQuiz();
        });

        // Results actions
        document.getElementById('newQuizBtn').addEventListener('click', () => {
            this.showScreen('welcomeScreen');
        });

        document.getElementById('reviewAnswersBtn').addEventListener('click', () => {
            this.reviewAnswers();
        });

        document.getElementById('saveResultsBtn').addEventListener('click', () => {
            this.saveResults();
        });

        // Header actions
        document.getElementById('themeToggle').addEventListener('click', () => {
            this.toggleTheme();
        });

        document.getElementById('settingsBtn').addEventListener('click', () => {
            this.showSettings();
        });

        document.getElementById('historyBtn').addEventListener('click', () => {
            this.showHistory();
        });

        document.getElementById('statsBtn').addEventListener('click', () => {
            this.showStatistics();
        });

        // Back to main menu buttons
        document.getElementById('backToMainFromQuiz').addEventListener('click', () => {
            this.confirmBackToMain('Are you sure you want to exit the quiz? Your progress will be lost.');
        });

        document.getElementById('backToMainFromQuestions').addEventListener('click', () => {
            this.showScreen('welcomeScreen');
        });

        // Settings actions
        document.getElementById('backToMainFromSettings').addEventListener('click', () => {
            this.showScreen('welcomeScreen');
        });

        document.getElementById('saveSettingsBtn').addEventListener('click', () => {
            this.saveSettings();
        });

        document.getElementById('resetSettingsBtn').addEventListener('click', () => {
            this.resetSettings();
        });

        // Theme select change listener
        document.getElementById('themeSelect').addEventListener('change', (e) => {
            this.setTheme(e.target.value);
        });

        // Download path browser

    }

    setupMenuListeners() {
        // Listen for menu events from main process
        window.electronAPI.onNewQuiz(() => {
            this.showScreen('welcomeScreen');
        });

        window.electronAPI.onGenerateMCQ(() => {
            this.selectQuestionType('MCQ');
            this.selectInputMethod('text');
        });

        window.electronAPI.onGenerateTF(() => {
            this.selectQuestionType('TF');
            this.selectInputMethod('text');
        });

        window.electronAPI.onStartQuiz(() => {
            if (this.currentQuestions.length > 0) {
                this.startInteractiveQuiz();
            } else {
                this.showNotification('No questions available. Please generate questions first.', 'warning');
            }
        });

        window.electronAPI.onSettings(() => {
            this.showSettings();
        });

        window.electronAPI.onStatistics(() => {
            this.showStatistics();
        });

        window.electronAPI.onHistory(() => {
            this.showHistory();
        });

        window.electronAPI.onAbout(() => {
            this.showAbout();
        });

        window.electronAPI.onHelp(() => {
            this.showHelp();
        });

        window.electronAPI.onFileSelected((event, filePath) => {
            this.handleExternalFile(filePath);
        });
    }

    setupDragAndDrop() {
        // File drop zone
        const fileDropZone = document.getElementById('fileDropZone');
        const imageDropZone = document.getElementById('imageDropZone');

        [fileDropZone, imageDropZone].forEach(zone => {
            zone.addEventListener('dragover', (e) => {
                e.preventDefault();
                zone.classList.add('dragover');
            });

            zone.addEventListener('dragleave', (e) => {
                e.preventDefault();
                zone.classList.remove('dragover');
            });

            zone.addEventListener('drop', async (e) => {
                e.preventDefault();
                zone.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const file = files[0];
                    // In Electron, we can access the file path directly from dropped files
                    if (file.path) {
                        if (zone === fileDropZone) {
                            await this.processFile({ path: file.path });
                        } else {
                            // Check if it's an image
                            const imageExtensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'];
                            const isImage = imageExtensions.some(ext =>
                                file.path.toLowerCase().endsWith(ext)
                            );

                            if (isImage) {
                                await this.processFile({ path: file.path });
                            } else {
                                this.showNotification('Please drop an image file', 'warning');
                            }
                        }
                    } else {
                        this.showNotification('Unable to access file path', 'error');
                    }
                }
            });

            zone.addEventListener('click', async () => {
                if (zone === fileDropZone) {
                    await this.generateFromFile();
                } else {
                    await this.generateFromImage();
                }
            });
        });
    }

    selectQuestionType(type) {
        this.selectedQuestionType = type;
        
        // Update UI
        document.querySelectorAll('.type-btn').forEach(btn => {
            btn.classList.remove('selected');
        });
        
        if (type === 'MCQ') {
            document.getElementById('mcqBtn').classList.add('selected');
        } else {
            document.getElementById('tfBtn').classList.add('selected');
        }

        this.showNotification(`Selected ${type} questions`, 'success');
    }

    selectInputMethod(method) {
        this.selectedInputMethod = method;
        
        if (!this.selectedQuestionType) {
            this.showNotification('Please select a question type first', 'warning');
            return;
        }

        this.showScreen('contentScreen');
        
        // Update screen title
        const title = document.getElementById('contentScreenTitle');
        title.textContent = `Add Content for ${this.selectedQuestionType} Questions`;

        // Show appropriate input area
        document.querySelectorAll('.input-area').forEach(area => {
            area.classList.remove('active');
        });

        switch (method) {
            case 'text':
                document.getElementById('textInputArea').classList.add('active');
                document.getElementById('textContent').focus();
                break;
            case 'file':
                document.getElementById('fileUploadArea').classList.add('active');
                break;
            case 'image':
                document.getElementById('imageUploadArea').classList.add('active');
                break;
        }
    }

    showScreen(screenId) {
        // Hide all screens
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });

        // Show target screen
        document.getElementById(screenId).classList.add('active');
        this.currentScreen = screenId;
    }

    showNotification(message, type = 'info') {
        const container = document.getElementById('notificationContainer');
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;

        container.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    async generateFromText() {
        const textContent = document.getElementById('textContent').value.trim();
        
        if (!textContent) {
            this.showNotification('Please enter some text content', 'warning');
            return;
        }

        if (textContent.length < 50) {
            this.showNotification('Please enter more content (at least 50 characters)', 'warning');
            return;
        }

        await this.generateQuestions(textContent);
    }

    async generateFromFile() {
        try {
            // Use file selection dialog for desktop app
            const fileSelection = await window.electronAPI.selectFile();
            if (fileSelection.success) {
                await this.processFile({ path: fileSelection.filePath });
            } else {
                this.showNotification('No file selected', 'warning');
            }
        } catch (error) {
            console.error('Error selecting file:', error);
            this.showNotification('Error selecting file', 'error');
        }
    }

    async generateFromImage() {
        try {
            // Use file selection dialog for desktop app (images)
            const fileSelection = await window.electronAPI.selectFile();
            if (fileSelection.success) {
                // Check if it's an image file
                const filePath = fileSelection.filePath;
                const imageExtensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'];
                const isImage = imageExtensions.some(ext =>
                    filePath.toLowerCase().endsWith(ext)
                );

                if (isImage) {
                    await this.processFile({ path: filePath });
                } else {
                    this.showNotification('Please select an image file', 'warning');
                }
            } else {
                this.showNotification('No image selected', 'warning');
            }
        } catch (error) {
            console.error('Error selecting image:', error);
            this.showNotification('Error selecting image', 'error');
        }
    }

    async processFile(file) {
        try {
            this.showProcessingScreen();
            this.updateProgress(10, 'Processing file...');

            // For desktop app, we need to use the file selection dialog
            // since we can't access file.path directly from the file input
            let filePath;

            if (file && file.path) {
                // If file has path (from drag & drop or external selection)
                filePath = file.path;
            } else {
                // Use file selection dialog
                const fileSelection = await window.electronAPI.selectFile();
                if (fileSelection.success) {
                    filePath = fileSelection.filePath;
                } else {
                    throw new Error('No file selected');
                }
            }

            // Process file through main process
            const result = await window.electronAPI.processFile(filePath);

            if (result.success) {
                this.updateProgress(50, 'Extracting text...');

                // Detect if this is an image file
                const imageExtensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'];
                const isImage = imageExtensions.some(ext =>
                    filePath.toLowerCase().endsWith(ext)
                );

                await this.generateQuestions(result.text, isImage);
            } else {
                throw new Error(result.error || 'Failed to process file');
            }
        } catch (error) {
            console.error('File processing error:', error);
            this.showNotification(`Error processing file: ${error.message}`, 'error');
            this.showScreen('contentScreen');
        }
    }

    async generateQuestions(content, isImage = false) {
        try {
            this.showProcessingScreen();
            this.updateProgress(60, 'Generating questions...');

            // Use the appropriate question count from settings
            let questionCount = 15; // Default fallback
            if (this.appSettings) {
                if (isImage) {
                    questionCount = this.appSettings.imageQuestionsCount || 15;
                } else {
                    questionCount = this.appSettings.questionsPerPage || 5;
                }
            }

            console.log(`Generating ${questionCount} questions (isImage: ${isImage})`);

            const questions = await window.electronAPI.generateQuestions(
                content,
                this.selectedQuestionType,
                questionCount
            );

            if (questions && questions.length > 0) {
                // Validate and clean up questions
                this.currentQuestions = this.validateAndCleanQuestions(questions);

                // For TF questions, ensure balanced answers
                if (this.selectedQuestionType === 'TF') {
                    this.currentQuestions = this.balanceTrueFalseAnswers(this.currentQuestions);
                }

                this.updateProgress(100, 'Questions generated successfully!');

                setTimeout(() => {
                    this.displayQuestions();
                }, 1000);
            } else {
                throw new Error('No questions were generated');
            }
        } catch (error) {
            console.error('Question generation error:', error);
            this.showNotification(`Error generating questions: ${error.message}`, 'error');
            this.showScreen('contentScreen');
        }
    }

    validateAndCleanQuestions(questions) {
        return questions.map((question, index) => {
            console.log(`Validating question ${index + 1}:`, question);

            // Clean up the question
            const cleanedQuestion = { ...question };

            // For True/False questions, ensure answer is properly formatted
            if (this.selectedQuestionType === 'TF') {
                if (!cleanedQuestion.answer || cleanedQuestion.answer === 'Unknown') {
                    // Use balanced assignment instead of defaulting to True
                    if (!this.tfBalanceTracker) {
                        this.tfBalanceTracker = { true: 0, false: 0, total: 0 };
                    }

                    const trueRatio = this.tfBalanceTracker.true / (this.tfBalanceTracker.total || 1);
                    let balancedAnswer;

                    if (trueRatio > 0.6) {
                        balancedAnswer = 'False';
                    } else if (trueRatio < 0.4) {
                        balancedAnswer = 'True';
                    } else {
                        balancedAnswer = Math.random() < 0.5 ? 'True' : 'False';
                    }

                    this.tfBalanceTracker.total++;
                    if (balancedAnswer === 'True') {
                        this.tfBalanceTracker.true++;
                    } else {
                        this.tfBalanceTracker.false++;
                    }

                    console.warn(`Question ${index + 1} has invalid answer:`, cleanedQuestion.answer, `using balanced assignment: ${balancedAnswer}`);
                    cleanedQuestion.answer = balancedAnswer;
                } else {
                    // Normalize the answer to ensure consistency
                    const normalized = this.normalizeAnswer(cleanedQuestion.answer);
                    cleanedQuestion.answer = normalized === 'true' ? 'True' : 'False';
                }

                // Validate True/False logic against explanation
                this.validateTrueFalseLogic(cleanedQuestion, index + 1);

                console.log(`Question ${index + 1} cleaned answer:`, cleanedQuestion.answer);
            }

            // Ensure question text exists
            if (!cleanedQuestion.question || cleanedQuestion.question.trim() === '') {
                cleanedQuestion.question = `Question ${index + 1}`;
                console.warn(`Question ${index + 1} has no text, using default`);
            }

            return cleanedQuestion;
        });
    }

    validateTrueFalseLogic(question, questionNumber) {
        if (!question.explanation) return;

        const questionText = question.question.toLowerCase();
        const explanation = question.explanation.toLowerCase();
        const answer = question.answer;

        console.log(`🔍 Validating TF logic for question ${questionNumber}:`);
        console.log(`   Question: "${question.question}"`);
        console.log(`   Answer: ${answer}`);
        console.log(`   Explanation: "${question.explanation}"`);

        // Check for common logic error patterns
        const falseIndicators = [
            'unknown', 'no specific', 'not identifiable', 'not known', 'unclear',
            'cannot be determined', 'no identifiable', 'idiopathic', 'primary',
            'essential', 'no clear cause'
        ];

        const trueIndicators = [
            'specific', 'identifiable', 'known cause', 'secondary', 'caused by',
            'results from', 'due to', 'because of', 'specific etiology'
        ];

        // Medical terminology validation
        this.validateMedicalTerminology(question, questionNumber);

        // Check if explanation contradicts the answer
        let explanationSuggestsFalse = falseIndicators.some(indicator =>
            explanation.includes(indicator)
        );

        let explanationSuggestsTrue = trueIndicators.some(indicator =>
            explanation.includes(indicator)
        );

        // Special case: Primary hypertension questions
        if (questionText.includes('primary') && questionText.includes('hypertension')) {
            if (questionText.includes('specific') || questionText.includes('identifiable')) {
                // Question asks if primary hypertension has specific cause
                // Correct answer should be False (primary = unknown cause)
                if (answer === 'True') {
                    console.warn(`⚠️  LOGIC ERROR DETECTED in question ${questionNumber}:`);
                    console.warn(`   Primary hypertension questions about "specific cause" should be FALSE`);
                    console.warn(`   Current answer: ${answer} - Consider changing to FALSE`);

                    // Auto-correct this common error
                    question.answer = 'False';
                    console.log(`✅ Auto-corrected answer to: False`);
                }
            }
        }

        // Special case: Angiotensin II vasodilator/vasoconstrictor questions
        if (questionText.includes('angiotensin ii') || questionText.includes('angiotensin 2')) {
            if (questionText.includes('vasodilator')) {
                // Question incorrectly calls angiotensin II a vasodilator
                // Angiotensin II is a vasoconstrictor, so this should be FALSE
                if (answer === 'True') {
                    console.warn(`⚠️  LOGIC ERROR DETECTED in question ${questionNumber}:`);
                    console.warn(`   Angiotensin II is a VASOCONSTRICTOR, not vasodilator`);
                    console.warn(`   Question calls it vasodilator - should be FALSE`);

                    // Auto-correct this medical error
                    question.answer = 'False';
                    console.log(`✅ Auto-corrected answer to: False`);
                }
            }
            if (questionText.includes('vasoconstrictor')) {
                // Question correctly calls angiotensin II a vasoconstrictor
                // This should be TRUE
                if (answer === 'False') {
                    console.warn(`⚠️  LOGIC ERROR DETECTED in question ${questionNumber}:`);
                    console.warn(`   Angiotensin II IS a vasoconstrictor`);
                    console.warn(`   Question correctly identifies it - should be TRUE`);

                    // Auto-correct this medical error
                    question.answer = 'True';
                    console.log(`✅ Auto-corrected answer to: True`);
                }
            }
        }

        // General contradiction check
        if (answer === 'True' && explanationSuggestsFalse && !explanationSuggestsTrue) {
            console.warn(`⚠️  POTENTIAL LOGIC ERROR in question ${questionNumber}:`);
            console.warn(`   Answer is TRUE but explanation suggests FALSE`);
            console.warn(`   False indicators found: ${falseIndicators.filter(i => explanation.includes(i))}`);
        }

        if (answer === 'False' && explanationSuggestsTrue && !explanationSuggestsFalse) {
            console.warn(`⚠️  POTENTIAL LOGIC ERROR in question ${questionNumber}:`);
            console.warn(`   Answer is FALSE but explanation suggests TRUE`);
            console.warn(`   True indicators found: ${trueIndicators.filter(i => explanation.includes(i))}`);
        }
    }

    validateMedicalTerminology(question, questionNumber) {
        const questionText = question.question.toLowerCase();
        const explanation = question.explanation ? question.explanation.toLowerCase() : '';
        const answer = question.answer;

        console.log(`🧠 Universal logic validation for question ${questionNumber}`);

        // Define universal logical fact patterns (works for any profession/subject)
        const universalLogicPatterns = [
            // Medical patterns (keep existing medical knowledge)
            {
                pattern: /angiotensin ii.*vasodilator/i,
                correctAnswer: 'False',
                reason: 'Angiotensin II is a vasoconstrictor, not a vasodilator'
            },
            {
                pattern: /angiotensin ii.*vasoconstrictor/i,
                correctAnswer: 'True',
                reason: 'Angiotensin II is indeed a vasoconstrictor'
            },
            {
                pattern: /ace inhibitors.*increase.*angiotensin ii/i,
                correctAnswer: 'False',
                reason: 'ACE inhibitors decrease angiotensin II by blocking its formation'
            },
            {
                pattern: /ace inhibitors.*decrease.*angiotensin ii/i,
                correctAnswer: 'True',
                reason: 'ACE inhibitors decrease angiotensin II by blocking its formation'
            },
            {
                pattern: /primary hypertension.*specific.*cause/i,
                correctAnswer: 'False',
                reason: 'Primary hypertension has no specific identifiable cause'
            },
            {
                pattern: /secondary hypertension.*specific.*cause/i,
                correctAnswer: 'True',
                reason: 'Secondary hypertension has specific identifiable causes'
            },
            {
                pattern: /insulin.*decrease.*blood glucose/i,
                correctAnswer: 'True',
                reason: 'Insulin decreases blood glucose levels'
            },
            {
                pattern: /insulin.*increase.*blood glucose/i,
                correctAnswer: 'False',
                reason: 'Insulin decreases, not increases, blood glucose levels'
            },
            {
                pattern: /microbiology.*solely.*bacterial/i,
                correctAnswer: 'False',
                reason: 'Microbiology labs handle viral, fungal, and parasitic infections, not solely bacterial'
            },
            {
                pattern: /microbiology.*only.*bacterial/i,
                correctAnswer: 'False',
                reason: 'Microbiology labs handle multiple types of pathogens, not only bacterial'
            },
            {
                pattern: /laboratories.*exclusively.*bacterial/i,
                correctAnswer: 'False',
                reason: 'Medical laboratories handle various types of infections, not exclusively bacterial'
            },

            // Engineering patterns
            {
                pattern: /software engineers.*only.*code/i,
                correctAnswer: 'False',
                reason: 'Software engineers also design, test, debug, document, and maintain systems'
            },
            {
                pattern: /civil engineers.*solely.*buildings/i,
                correctAnswer: 'False',
                reason: 'Civil engineers also work on bridges, roads, dams, and infrastructure systems'
            },

            // Legal patterns
            {
                pattern: /lawyers.*exclusively.*court/i,
                correctAnswer: 'False',
                reason: 'Lawyers also provide legal advice, draft documents, negotiate, and handle transactions'
            },
            {
                pattern: /judges.*only.*criminal cases/i,
                correctAnswer: 'False',
                reason: 'Judges handle criminal, civil, family, and administrative cases'
            },

            // Education patterns
            {
                pattern: /teachers.*solely.*lecture/i,
                correctAnswer: 'False',
                reason: 'Teachers also facilitate discussions, assess students, provide feedback, and mentor'
            },
            {
                pattern: /professors.*only.*research/i,
                correctAnswer: 'False',
                reason: 'Professors also teach, mentor students, serve on committees, and engage in service'
            },

            // Business patterns
            {
                pattern: /accountants.*exclusively.*taxes/i,
                correctAnswer: 'False',
                reason: 'Accountants also handle auditing, financial reporting, budgeting, and advisory services'
            },
            {
                pattern: /managers.*only.*supervise/i,
                correctAnswer: 'False',
                reason: 'Managers also plan, organize, coordinate, and make strategic decisions'
            },

            // Technology patterns
            {
                pattern: /databases.*solely.*store.*data/i,
                correctAnswer: 'False',
                reason: 'Databases also retrieve, organize, secure, backup, and process data'
            },
            {
                pattern: /firewalls.*only.*block/i,
                correctAnswer: 'False',
                reason: 'Firewalls also monitor, log, filter, and allow authorized traffic'
            }
        ];

        // Check each universal logic pattern
        for (const fact of universalLogicPatterns) {
            if (fact.pattern.test(questionText)) {
                console.log(`   📋 Medical pattern matched: ${fact.pattern}`);
                console.log(`   📚 Medical fact: ${fact.reason}`);
                console.log(`   🎯 Expected answer: ${fact.correctAnswer}`);
                console.log(`   🤖 AI answer: ${answer}`);

                if (answer !== fact.correctAnswer) {
                    console.warn(`⚠️  LOGIC ERROR DETECTED in question ${questionNumber}:`);
                    console.warn(`   Pattern: ${fact.pattern}`);
                    console.warn(`   Logic rule: ${fact.reason}`);
                    console.warn(`   AI gave: ${answer}, should be: ${fact.correctAnswer}`);

                    // Auto-correct logic errors
                    question.answer = fact.correctAnswer;
                    console.log(`✅ Auto-corrected logic error: ${answer} → ${fact.correctAnswer}`);

                    // Update explanation if it contradicts the correction
                    if (question.explanation && !question.explanation.toLowerCase().includes(fact.reason.toLowerCase())) {
                        question.explanation += ` (Note: ${fact.reason})`;
                        console.log(`📝 Enhanced explanation with logical fact`);
                    }
                }
                break; // Only apply first matching pattern
            }
        }

        // Check for explanation contradictions
        if (explanation) {
            // If explanation mentions "vasoconstrictor" but question says "vasodilator"
            if (questionText.includes('vasodilator') && explanation.includes('vasoconstrictor')) {
                if (answer === 'True') {
                    console.warn(`⚠️  CONTRADICTION DETECTED in question ${questionNumber}:`);
                    console.warn(`   Question mentions 'vasodilator' but explanation says 'vasoconstrictor'`);
                    console.warn(`   Auto-correcting to FALSE`);
                    question.answer = 'False';
                }
            }

            // If explanation mentions "vasodilator" but question says "vasoconstrictor"
            if (questionText.includes('vasoconstrictor') && explanation.includes('vasodilator')) {
                if (answer === 'True') {
                    console.warn(`⚠️  CONTRADICTION DETECTED in question ${questionNumber}:`);
                    console.warn(`   Question mentions 'vasoconstrictor' but explanation says 'vasodilator'`);
                    console.warn(`   Auto-correcting to FALSE`);
                    question.answer = 'False';
                }
            }

            // Universal Logic Contradiction Detection
            this.detectUniversalContradictions(question, questionNumber, questionText, explanation, answer);
        }
    }

    balanceTrueFalseAnswers(questions) {
        if (!questions || questions.length === 0) return questions;

        // Count current true/false distribution
        let trueCount = 0;
        let falseCount = 0;

        questions.forEach(q => {
            const normalized = this.normalizeAnswer(q.answer);
            if (normalized === 'true') {
                trueCount++;
            } else {
                falseCount++;
            }
        });

        console.log(`Original TF distribution: ${trueCount} True, ${falseCount} False`);

        // If distribution is severely imbalanced (more than 70% one way), rebalance some answers
        const total = questions.length;
        const trueRatio = trueCount / total;

        if (trueRatio > 0.7 || trueRatio < 0.3) {
            console.log(`Rebalancing TF answers - current ratio: ${(trueRatio * 100).toFixed(1)}% true`);

            const targetTrue = Math.floor(total / 2);
            const targetFalse = total - targetTrue;

            let adjustedTrue = 0;
            let adjustedFalse = 0;

            // First pass: keep answers that seem logically correct
            questions.forEach((question, index) => {
                const currentAnswer = this.normalizeAnswer(question.answer);
                const explanation = question.explanation?.toLowerCase() || '';

                // Check if the explanation strongly suggests the current answer
                const stronglyTrue = explanation.includes('correct') || explanation.includes('true') ||
                                   explanation.includes('accurate') || explanation.includes('valid');
                const stronglyFalse = explanation.includes('incorrect') || explanation.includes('false') ||
                                     explanation.includes('wrong') || explanation.includes('invalid');

                if ((currentAnswer === 'true' && stronglyTrue) || (currentAnswer === 'false' && stronglyFalse)) {
                    // Keep this answer as is
                    if (currentAnswer === 'true') adjustedTrue++;
                    else adjustedFalse++;
                } else {
                    // Mark for potential adjustment
                    question._needsRebalance = true;
                }
            });

            // Second pass: adjust answers that don't have strong logical indicators
            questions.forEach((question, index) => {
                if (question._needsRebalance) {
                    if (adjustedTrue < targetTrue) {
                        question.answer = 'True';
                        adjustedTrue++;
                    } else if (adjustedFalse < targetFalse) {
                        question.answer = 'False';
                        adjustedFalse++;
                    }
                    delete question._needsRebalance;
                }
            });

            console.log(`Rebalanced TF distribution: ${adjustedTrue} True, ${adjustedFalse} False`);
        }

        return questions;
    }

    showProcessingScreen() {
        this.showScreen('processingScreen');
        this.updateProgress(0, 'Starting...');
    }

    updateProgress(percentage, status) {
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const processingStatus = document.getElementById('processingStatus');

        progressFill.style.width = `${percentage}%`;
        progressText.textContent = `${percentage}%`;
        processingStatus.textContent = status;
    }

    displayQuestions() {
        this.showScreen('questionsScreen');
        this.showQuestionsOnly();
    }

    showQuestionsOnly() {
        const questionsDisplay = document.getElementById('questionsDisplay');
        questionsDisplay.innerHTML = '';

        this.currentQuestions.forEach((question, index) => {
            const questionElement = this.createQuestionElement(question, index, false); // false = hide answers
            questionsDisplay.appendChild(questionElement);
        });
    }

    showQuestionsWithAnswers() {
        const questionsDisplay = document.getElementById('questionsDisplay');
        questionsDisplay.innerHTML = '';

        this.currentQuestions.forEach((question, index) => {
            const questionElement = this.createQuestionElement(question, index, true); // true = show answers
            questionsDisplay.appendChild(questionElement);
        });
    }

    createQuestionElement(question, index, showAnswers = true) {
        const questionDiv = document.createElement('div');
        questionDiv.className = 'question-item';

        let optionsHtml = '';
        if (question.options) {
            // MCQ question
            optionsHtml = question.options.map((option, optIndex) => {
                const letter = String.fromCharCode(65 + optIndex); // A, B, C, D
                const isCorrect = option === question.answer;
                return `
                    <div class="option-item ${showAnswers && isCorrect ? 'correct' : ''}">
                        <div class="option-letter">${letter}</div>
                        <span>${option}</span>
                    </div>
                `;
            }).join('');
        } else {
            // True/False question - use normalized comparison
            const normalizedAnswer = this.normalizeAnswer(question.answer);
            optionsHtml = `
                <div class="option-item ${showAnswers && normalizedAnswer === 'true' ? 'correct' : ''}">
                    <div class="option-letter">T</div>
                    <span>True</span>
                </div>
                <div class="option-item ${showAnswers && normalizedAnswer === 'false' ? 'correct' : ''}">
                    <div class="option-letter">F</div>
                    <span>False</span>
                </div>
            `;
        }

        questionDiv.innerHTML = `
            <div class="question-header">
                <div class="question-number">${index + 1}</div>
                <div class="question-text">${question.question}</div>
            </div>
            <div class="question-options">
                ${optionsHtml}
            </div>
            ${showAnswers ? `
                <div class="question-answer">
                    <div class="answer-label">Correct Answer:</div>
                    <div>${this.formatAnswerForDisplay(question.answer, this.selectedQuestionType)}</div>
                </div>
                ${question.explanation ? `
                    <div class="question-explanation">
                        <div class="explanation-label">Explanation:</div>
                        <div>${question.explanation}</div>
                    </div>
                ` : ''}
            ` : ''}
        `;

        return questionDiv;
    }

    startInteractiveQuiz() {
        if (!this.currentQuestions || this.currentQuestions.length === 0) {
            this.showNotification('No questions available for quiz', 'warning');
            return;
        }

        // Initialize quiz state
        this.quizState = {
            currentQuestionIndex: 0,
            answers: [],
            score: { correct: 0, total: this.currentQuestions.length },
            startTime: new Date(),
            endTime: null
        };

        this.showScreen('quizScreen');
        this.displayQuizQuestion();
    }

    displayQuizQuestion() {
        const currentQuestion = this.currentQuestions[this.quizState.currentQuestionIndex];
        const questionNumber = this.quizState.currentQuestionIndex + 1;
        const totalQuestions = this.currentQuestions.length;

        console.log(`Displaying question ${questionNumber} of ${totalQuestions}`);
        console.log('Question:', currentQuestion.question);

        // Update header
        document.getElementById('questionNumber').textContent = `Question ${questionNumber}`;
        document.getElementById('questionCount').textContent = `of ${totalQuestions}`;
        document.getElementById('currentScore').textContent =
            `${this.quizState.score.correct}/${this.quizState.currentQuestionIndex}`;

        // Display question
        document.getElementById('quizQuestion').textContent = currentQuestion.question;

        // Display options
        const optionsContainer = document.getElementById('quizOptions');
        optionsContainer.innerHTML = '';

        if (currentQuestion.options) {
            // MCQ question
            currentQuestion.options.forEach((option, index) => {
                const optionElement = document.createElement('div');
                optionElement.className = 'quiz-option';
                optionElement.dataset.answer = option;

                optionElement.innerHTML = `
                    <div class="option-indicator">${String.fromCharCode(65 + index)}</div>
                    <span>${option}</span>
                `;

                optionElement.addEventListener('click', () => {
                    this.selectQuizOption(optionElement);
                });

                optionsContainer.appendChild(optionElement);
            });
        } else {
            // True/False question
            ['True', 'False'].forEach((option, index) => {
                const optionElement = document.createElement('div');
                optionElement.className = 'quiz-option';
                optionElement.dataset.answer = option;

                optionElement.innerHTML = `
                    <div class="option-indicator">${option[0]}</div>
                    <span>${option}</span>
                `;

                optionElement.addEventListener('click', () => {
                    this.selectQuizOption(optionElement);
                });

                optionsContainer.appendChild(optionElement);
            });
        }

        // Reset UI state
        document.getElementById('quizFeedback').classList.add('hidden');
        document.getElementById('submitAnswer').disabled = true;
        document.getElementById('submitAnswer').classList.remove('hidden'); // Make sure submit button is visible
        document.getElementById('nextQuestion').classList.add('hidden');
        document.getElementById('finishQuiz').classList.add('hidden');

        // Re-enable option selection for new question
        document.querySelectorAll('.quiz-option').forEach(option => {
            option.style.pointerEvents = 'auto';
            option.classList.remove('selected', 'correct', 'incorrect');
        });
    }

    selectQuizOption(selectedElement) {
        // Remove previous selection
        document.querySelectorAll('.quiz-option').forEach(option => {
            option.classList.remove('selected');
        });

        // Select current option
        selectedElement.classList.add('selected');
        document.getElementById('submitAnswer').disabled = false;
    }

    submitQuizAnswer() {
        const selectedOption = document.querySelector('.quiz-option.selected');
        if (!selectedOption) {
            console.log('No option selected');
            return;
        }

        const userAnswer = selectedOption.dataset.answer;
        const currentQuestion = this.currentQuestions[this.quizState.currentQuestionIndex];

        // Normalize answers for comparison (handle case sensitivity and variations)
        const normalizedUserAnswer = this.normalizeAnswer(userAnswer);
        const normalizedCorrectAnswer = this.normalizeAnswer(currentQuestion.answer);
        const isCorrect = normalizedUserAnswer === normalizedCorrectAnswer;

        console.log(`Question ${this.quizState.currentQuestionIndex + 1}:`);
        console.log(`  Question text: "${currentQuestion.question}"`);
        console.log(`  User answered: "${userAnswer}" (normalized: "${normalizedUserAnswer}")`);
        console.log(`  Correct answer: "${currentQuestion.answer}" (normalized: "${normalizedCorrectAnswer}")`);
        console.log(`  Explanation: "${currentQuestion.explanation || 'No explanation'}"`);
        console.log(`  Is correct: ${isCorrect}`);

        // Additional validation for True/False questions
        if (this.selectedQuestionType === 'TF') {
            console.log(`  TF Question Analysis:`);
            console.log(`    - Question asks if statement is true`);
            console.log(`    - AI says correct answer is: ${currentQuestion.answer}`);
            console.log(`    - User selected: ${userAnswer}`);
            console.log(`    - Match result: ${isCorrect}`);

            // Check for potential logic errors
            if (currentQuestion.explanation) {
                const explanation = currentQuestion.explanation.toLowerCase();
                if ((explanation.includes('unknown') || explanation.includes('no specific') || explanation.includes('not identifiable')) &&
                    normalizedCorrectAnswer === 'true') {
                    console.warn(`  ⚠️  POTENTIAL LOGIC ERROR: Explanation suggests FALSE but answer is TRUE`);
                }
                if ((explanation.includes('specific') || explanation.includes('identifiable') || explanation.includes('known cause')) &&
                    normalizedCorrectAnswer === 'false') {
                    console.warn(`  ⚠️  POTENTIAL LOGIC ERROR: Explanation suggests TRUE but answer is FALSE`);
                }
            }
        }

        // Record answer
        this.quizState.answers.push({
            questionIndex: this.quizState.currentQuestionIndex,
            userAnswer: userAnswer,
            correctAnswer: currentQuestion.answer,
            isCorrect: isCorrect,
            question: currentQuestion.question
        });

        if (isCorrect) {
            this.quizState.score.correct++;
        }

        // Show feedback
        this.showQuizFeedback(isCorrect, currentQuestion, userAnswer);

        // Update UI - Hide submit button and show appropriate next button
        const submitBtn = document.getElementById('submitAnswer');
        const nextBtn = document.getElementById('nextQuestion');
        const finishBtn = document.getElementById('finishQuiz');

        submitBtn.classList.add('hidden');

        const isLastQuestion = this.quizState.currentQuestionIndex >= this.currentQuestions.length - 1;
        console.log(`Current question index: ${this.quizState.currentQuestionIndex}, Total questions: ${this.currentQuestions.length}, Is last question: ${isLastQuestion}`);

        if (isLastQuestion) {
            nextBtn.classList.add('hidden');
            finishBtn.classList.remove('hidden');
            console.log('Showing finish button');
        } else {
            finishBtn.classList.add('hidden');
            nextBtn.classList.remove('hidden');
            console.log('Showing next button');
        }

        // Disable option selection and highlight correct/incorrect answers
        document.querySelectorAll('.quiz-option').forEach(option => {
            option.style.pointerEvents = 'none';

            // Use normalized comparison for highlighting
            const optionAnswer = this.normalizeAnswer(option.dataset.answer);
            const correctAnswer = this.normalizeAnswer(currentQuestion.answer);

            if (optionAnswer === correctAnswer) {
                option.classList.add('correct');
            } else if (option.classList.contains('selected') && !isCorrect) {
                option.classList.add('incorrect');
            }
        });
    }

    normalizeAnswer(answer) {
        if (!answer && answer !== 0 && answer !== false) return '';

        // Handle boolean values
        if (answer === true || answer === false) {
            return answer ? 'true' : 'false';
        }

        // Convert to string and normalize
        const normalized = String(answer).toLowerCase().trim();

        // Handle common variations - expanded patterns
        if (normalized === 'true' || normalized === 't' || normalized === '1' ||
            normalized === 'yes' || normalized === 'correct' || normalized === 'right' ||
            normalized === 'verdadero' || normalized === 'vrai' || normalized === 'wahr') {
            return 'true';
        }

        if (normalized === 'false' || normalized === 'f' || normalized === '0' ||
            normalized === 'no' || normalized === 'incorrect' || normalized === 'wrong' ||
            normalized === 'falso' || normalized === 'faux' || normalized === 'falsch') {
            return 'false';
        }

        // Check if it contains true/false keywords
        if (normalized.includes('true') || normalized.includes('correct') || normalized.includes('yes')) {
            return 'true';
        }

        if (normalized.includes('false') || normalized.includes('incorrect') || normalized.includes('no')) {
            return 'false';
        }

        // For MCQ, return as-is but normalized
        return normalized;
    }

    formatAnswerForDisplay(answer, questionType = null) {
        if (!answer && answer !== 0 && answer !== false) {
            console.warn('formatAnswerForDisplay: Empty or null answer received:', answer);
            return questionType === 'TF' ? 'True' : 'Unknown'; // Default to True for TF questions
        }

        const normalized = String(answer).toLowerCase().trim();
        console.log('formatAnswerForDisplay: Processing answer:', answer, 'normalized:', normalized);

        // Handle boolean values
        if (answer === true || answer === false) {
            return answer ? 'True' : 'False';
        }

        // Format True/False answers nicely - expanded patterns
        if (normalized === 'true' || normalized === 't' || normalized === '1' ||
            normalized === 'yes' || normalized === 'correct' || normalized === 'right' ||
            normalized === 'verdadero' || normalized === 'vrai' || normalized === 'wahr') {
            return 'True';
        }

        if (normalized === 'false' || normalized === 'f' || normalized === '0' ||
            normalized === 'no' || normalized === 'incorrect' || normalized === 'wrong' ||
            normalized === 'falso' || normalized === 'faux' || normalized === 'falsch') {
            return 'False';
        }

        // Check if it contains true/false keywords
        if (normalized.includes('true') || normalized.includes('correct') || normalized.includes('yes')) {
            console.log('formatAnswerForDisplay: Found True keyword in:', normalized);
            return 'True';
        }

        if (normalized.includes('false') || normalized.includes('incorrect') || normalized.includes('no')) {
            console.log('formatAnswerForDisplay: Found False keyword in:', normalized);
            return 'False';
        }

        // For True/False questions, if we can't determine the answer, default to True and log warning
        if (questionType === 'TF') {
            console.warn('formatAnswerForDisplay: Could not determine True/False for answer:', answer, 'defaulting to True');
            return 'True';
        }

        // For MCQ answers, return the original answer
        return String(answer);
    }

    showQuizFeedback(isCorrect, question, userAnswer) {
        const feedbackElement = document.getElementById('quizFeedback');
        feedbackElement.className = `quiz-feedback ${isCorrect ? 'feedback-correct' : 'feedback-incorrect'}`;

        const formattedCorrectAnswer = this.formatAnswerForDisplay(question.answer, this.selectedQuestionType);
        const formattedUserAnswer = this.formatAnswerForDisplay(userAnswer, this.selectedQuestionType);

        feedbackElement.innerHTML = `
            <div class="feedback-title">
                ${isCorrect ? '✅ Correct!' : '❌ Incorrect'}
            </div>
            <div>
                ${isCorrect
                    ? `You selected: <strong>${formattedUserAnswer}</strong> ✓`
                    : `You selected: <strong>${formattedUserAnswer}</strong><br>The correct answer is: <strong>${formattedCorrectAnswer}</strong>`
                }
            </div>
            ${question.explanation ? `<div style="margin-top: 0.5rem;"><strong>Explanation:</strong> ${question.explanation}</div>` : ''}
        `;

        feedbackElement.classList.remove('hidden');
    }

    nextQuizQuestion() {
        console.log(`Moving to next question. Current index: ${this.quizState.currentQuestionIndex}, Total questions: ${this.currentQuestions.length}`);

        this.quizState.currentQuestionIndex++;

        if (this.quizState.currentQuestionIndex >= this.currentQuestions.length) {
            console.log('Reached end of quiz, finishing...');
            this.finishQuiz();
            return;
        }

        console.log(`Displaying question ${this.quizState.currentQuestionIndex + 1}`);
        this.displayQuizQuestion();
    }

    finishQuiz() {
        this.quizState.endTime = new Date();
        this.showQuizResults();
    }

    showQuizResults() {
        this.showScreen('resultsScreen');

        const score = this.quizState.score;
        const percentage = Math.round((score.correct / score.total) * 100);
        const duration = Math.round((this.quizState.endTime - this.quizState.startTime) / 1000);

        const resultsDisplay = document.getElementById('resultsDisplay');
        resultsDisplay.innerHTML = `
            <div class="score-summary">
                <div class="score-item">
                    <span class="score-value">${percentage}%</span>
                    <div class="score-label">Overall Score</div>
                </div>
                <div class="score-item">
                    <span class="score-value">${score.correct}</span>
                    <div class="score-label">Correct Answers</div>
                </div>
                <div class="score-item">
                    <span class="score-value">${score.total - score.correct}</span>
                    <div class="score-label">Incorrect Answers</div>
                </div>
                <div class="score-item">
                    <span class="score-value">${duration}s</span>
                    <div class="score-label">Time Taken</div>
                </div>
            </div>
            <div style="text-align: center; margin-top: 2rem;">
                <h3>Performance: ${this.getPerformanceLevel(percentage)}</h3>
                <p style="color: #666; margin-top: 0.5rem;">
                    ${this.getPerformanceMessage(percentage)}
                </p>
            </div>
        `;

        // Save quiz session
        this.saveQuizSession();
    }

    getPerformanceLevel(percentage) {
        if (percentage >= 90) return 'Excellent! 🏆';
        if (percentage >= 80) return 'Very Good! 🌟';
        if (percentage >= 70) return 'Good! 👍';
        if (percentage >= 60) return 'Fair 📚';
        return 'Needs Improvement 💪';
    }

    getPerformanceMessage(percentage) {
        if (percentage >= 90) return 'Outstanding performance! You have mastered this topic.';
        if (percentage >= 80) return 'Great job! You have a strong understanding of the material.';
        if (percentage >= 70) return 'Well done! Consider reviewing the topics you missed.';
        if (percentage >= 60) return 'You\'re on the right track. More practice will help improve your score.';
        return 'Keep studying! Review the material and try again to improve your understanding.';
    }

    async saveQuizSession() {
        try {
            const session = {
                timestamp: new Date().toISOString(),
                questionType: this.selectedQuestionType,
                score: this.quizState.score,
                answers: this.quizState.answers,
                duration: this.quizState.endTime - this.quizState.startTime,
                questions: this.currentQuestions
            };

            await window.electronAPI.saveQuizSession(session);
        } catch (error) {
            console.error('Error saving quiz session:', error);
        }
    }

    handleFileSelect(file) {
        if (!file) return;

        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const generateBtn = document.getElementById('generateFromFile');

        fileName.textContent = file.name;
        fileSize.textContent = this.formatFileSize(file.size);
        fileInfo.classList.remove('hidden');
        generateBtn.disabled = false;
    }

    handleImageSelect(file) {
        if (!file) return;

        const imagePreview = document.getElementById('imagePreview');
        const previewImg = document.getElementById('previewImg');
        const generateBtn = document.getElementById('generateFromImage');

        const reader = new FileReader();
        reader.onload = (e) => {
            previewImg.src = e.target.result;
            imagePreview.classList.remove('hidden');
            generateBtn.disabled = false;
        };
        reader.readAsDataURL(file);
    }

    removeFile() {
        document.getElementById('fileInput').value = '';
        document.getElementById('fileInfo').classList.add('hidden');
        document.getElementById('generateFromFile').disabled = true;
    }

    removeImage() {
        document.getElementById('imageInput').value = '';
        document.getElementById('imagePreview').classList.add('hidden');
        document.getElementById('generateFromImage').disabled = true;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Settings Management
    async showSettings() {
        this.showScreen('settingsScreen');
        await this.loadSettings();
    }

    async loadSettings() {
        try {
            // Get settings from electron main process
            const result = await window.electronAPI.getSettings();

            if (result.success) {
                const settings = result.settings;

                // Update app's internal settings
                this.appSettings = settings;

                // Load settings into UI
                document.getElementById('questionsPerPage').value = settings.questionsPerPage || 5;
                document.getElementById('imageQuestionsCount').value = settings.imageQuestionsCount || 15;
                document.getElementById('shuffleQuestions').checked = settings.shuffleQuestions || false;
                document.getElementById('shuffleOptions').checked = settings.shuffleOptions || false;
                document.getElementById('showExplanations').checked = settings.showExplanations !== false;
                document.getElementById('notifications').checked = settings.notifications !== false;
                document.getElementById('autoOpenPDF').checked = settings.autoOpenPDF !== false;

                // Set theme select to saved value
                document.getElementById('themeSelect').value = settings.theme || 'light';

                console.log('Settings loaded successfully');
            } else {
                console.error('Failed to load settings:', result.error);
                this.showNotification('Failed to load settings', 'error');
            }
        } catch (error) {
            console.error('Error loading settings:', error);
            this.showNotification('Error loading settings', 'error');
        }
    }

    async saveSettings() {
        try {
            // Collect settings from UI
            const settings = {
                questionsPerPage: parseInt(document.getElementById('questionsPerPage').value),
                imageQuestionsCount: parseInt(document.getElementById('imageQuestionsCount').value),
                shuffleQuestions: document.getElementById('shuffleQuestions').checked,
                shuffleOptions: document.getElementById('shuffleOptions').checked,
                showExplanations: document.getElementById('showExplanations').checked,
                notifications: document.getElementById('notifications').checked,
                autoOpenPDF: document.getElementById('autoOpenPDF').checked,
                theme: document.getElementById('themeSelect').value
            };

            // Validate settings
            if (settings.questionsPerPage < 1 || settings.questionsPerPage > 50) {
                this.showNotification('Questions per page must be between 1 and 50', 'error');
                return;
            }

            if (settings.imageQuestionsCount < 5 || settings.imageQuestionsCount > 50) {
                this.showNotification('Image questions count must be between 5 and 50', 'error');
                return;
            }

            // Don't apply theme during save - let user manually change it via theme toggle
            // Theme will be applied on next app startup from saved settings

            // Save settings via electron main process
            const result = await window.electronAPI.saveSettings(settings);

            if (result.success) {
                // Update app's internal settings
                this.appSettings = { ...this.appSettings, ...settings };
                this.showNotification('Settings saved successfully!', 'success');
                console.log('Settings saved:', settings);
            } else {
                console.error('Failed to save settings:', result.error);
                this.showNotification('Failed to save settings', 'error');
            }
        } catch (error) {
            console.error('Error saving settings:', error);
            this.showNotification('Error saving settings', 'error');
        }
    }

    resetSettings() {
        if (confirm('Are you sure you want to reset all settings to their default values?')) {
            // Reset to default values
            document.getElementById('questionsPerPage').value = 5;
            document.getElementById('imageQuestionsCount').value = 15;
            document.getElementById('shuffleQuestions').checked = false;
            document.getElementById('shuffleOptions').checked = false;
            document.getElementById('showExplanations').checked = true;
            document.getElementById('notifications').checked = true;
            document.getElementById('autoOpenPDF').checked = true;
            document.getElementById('themeSelect').value = 'light';

            // Apply light theme
            this.setTheme('light');

            this.showNotification('Settings reset to defaults', 'info');
        }
    }



    showHistory() {
        this.showNotification('Quiz history coming soon!', 'info');
    }

    showStatistics() {
        this.showNotification('Statistics panel coming soon!', 'info');
    }

    showAbout() {
        this.showNotification('About dialog coming soon!', 'info');
    }

    showHelp() {
        this.showNotification('Help documentation coming soon!', 'info');
    }

    reviewAnswers() {
        this.showScreen('questionsScreen');
    }

    saveResults() {
        this.showNotification('Results saved successfully!', 'success');
    }

    async exportQuestions() {
        if (!this.currentQuestions || this.currentQuestions.length === 0) {
            this.showNotification('No questions available to export', 'warning');
            return;
        }

        try {
            this.showNotification('Exporting questions...', 'info');

            const result = await window.electronAPI.exportQuestions(this.currentQuestions, 'pdf');

            if (result.success && !result.cancelled) {
                this.showNotification('Questions exported successfully!', 'success');

                // Check if auto-open is enabled
                const autoOpenPDF = document.getElementById('autoOpenPDF')?.checked;
                if (autoOpenPDF && result.filePath) {
                    await window.electronAPI.openExternal(`file://${result.filePath}`);
                }
            } else if (result.cancelled) {
                // User cancelled, no notification needed
            } else {
                throw new Error(result.error || 'Export failed');
            }
        } catch (error) {
            console.error('Export error:', error);
            this.showNotification(`Export failed: ${error.message}`, 'error');
        }
    }

    handleExternalFile(filePath) {
        this.showNotification(`File selected: ${filePath}`, 'info');
        // Handle external file selection from menu
    }

    detectUniversalContradictions(question, questionNumber, questionText, explanation, answer) {
        console.log(`🔍 Universal contradiction analysis for question ${questionNumber}`);

        // 1. Exclusivity vs. Plurality Contradictions
        const exclusiveWords = ['solely', 'only', 'exclusively', 'just', 'merely', 'purely', 'simply', 'uniquely'];
        const pluralityWords = ['also', 'additionally', 'furthermore', 'not just', 'not only', 'more than', 'as well as', 'including', 'plus', 'and', 'various', 'multiple', 'several'];

        const hasExclusiveWord = exclusiveWords.some(word => questionText.includes(word));
        const hasPluralityWord = pluralityWords.some(word => explanation.includes(word));

        if (hasExclusiveWord && hasPluralityWord && answer === 'True') {
            console.warn(`⚠️  EXCLUSIVITY CONTRADICTION DETECTED in question ${questionNumber}:`);
            console.warn(`   Question claims exclusivity but explanation suggests plurality`);
            console.warn(`   Exclusive words: ${exclusiveWords.filter(w => questionText.includes(w))}`);
            console.warn(`   Plurality words in explanation: ${pluralityWords.filter(w => explanation.includes(w))}`);
            console.warn(`   Auto-correcting to FALSE`);
            question.answer = 'False';
            return;
        }

        // 2. Absolute vs. Conditional Contradictions
        const absoluteWords = ['always', 'never', 'all', 'none', 'every', 'no', 'completely', 'totally', 'entirely', 'absolutely'];
        const conditionalWords = ['sometimes', 'often', 'usually', 'typically', 'generally', 'most', 'some', 'may', 'might', 'can', 'could', 'depends', 'varies'];

        const hasAbsoluteWord = absoluteWords.some(word => questionText.includes(word));
        const hasConditionalWord = conditionalWords.some(word => explanation.includes(word));

        if (hasAbsoluteWord && hasConditionalWord && answer === 'True') {
            console.warn(`⚠️  ABSOLUTE vs CONDITIONAL CONTRADICTION DETECTED in question ${questionNumber}:`);
            console.warn(`   Question uses absolute terms but explanation suggests conditions/exceptions`);
            console.warn(`   Absolute words: ${absoluteWords.filter(w => questionText.includes(w))}`);
            console.warn(`   Conditional words in explanation: ${conditionalWords.filter(w => explanation.includes(w))}`);
            console.warn(`   Auto-correcting to FALSE`);
            question.answer = 'False';
            return;
        }

        // 3. Scope Limitation vs. Broad Scope Contradictions
        const limitingWords = ['limited to', 'restricted to', 'confined to', 'specific to', 'dedicated to'];
        const broadScopeWords = ['wide range', 'broad spectrum', 'various types', 'multiple areas', 'diverse', 'comprehensive'];

        const hasLimitingWord = limitingWords.some(phrase => questionText.includes(phrase));
        const hasBroadScopeWord = broadScopeWords.some(phrase => explanation.includes(phrase));

        if (hasLimitingWord && hasBroadScopeWord && answer === 'True') {
            console.warn(`⚠️  SCOPE CONTRADICTION DETECTED in question ${questionNumber}:`);
            console.warn(`   Question suggests limited scope but explanation indicates broad scope`);
            console.warn(`   Auto-correcting to FALSE`);
            question.answer = 'False';
            return;
        }

        console.log(`✅ No universal contradictions detected in question ${questionNumber}`);
    }

    // Theme Management
    async initializeTheme() {
        try {
            // Load theme from database settings
            const result = await window.electronAPI.getSettings();
            if (result.success && result.settings.theme) {
                this.setTheme(result.settings.theme);
            } else {
                // Fallback to localStorage or default
                const savedTheme = localStorage.getItem('app-theme') || 'light';
                this.setTheme(savedTheme);
            }
        } catch (error) {
            console.warn('Could not load theme from settings, using default:', error);
            this.setTheme('light');
        }
    }

    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);
    }

    async setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);

        // Save to database (but don't wait for it to avoid blocking UI)
        this.saveThemeToDatabase(theme);

        // Update theme toggle icon
        const themeIcon = document.querySelector('#themeToggle i');
        if (themeIcon) {
            themeIcon.className = theme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }

        // Update theme toggle title
        const themeBtn = document.getElementById('themeToggle');
        if (themeBtn) {
            themeBtn.title = theme === 'light' ? 'Switch to Dark Mode' : 'Switch to Light Mode';
        }

        // Update theme select in settings if it exists
        const themeSelect = document.getElementById('themeSelect');
        if (themeSelect) {
            themeSelect.value = theme;
        }

        console.log(`Theme switched to: ${theme}`);
    }

    async saveThemeToDatabase(theme) {
        try {
            // Save only the theme setting
            const result = await window.electronAPI.saveSettings({ theme: theme });
            if (!result.success) {
                console.warn('Failed to save theme to database:', result.error);
                // Fallback to localStorage
                localStorage.setItem('app-theme', theme);
            }
        } catch (error) {
            console.warn('Error saving theme to database:', error);
            // Fallback to localStorage
            localStorage.setItem('app-theme', theme);
        }
    }

    // Navigation Confirmation
    confirmBackToMain(message) {
        if (confirm(message)) {
            this.showScreen('welcomeScreen');
            // Reset quiz state if in progress
            if (this.quizState) {
                this.quizState = {
                    currentQuestionIndex: 0,
                    answers: [],
                    score: { correct: 0, total: 0 },
                    startTime: null,
                    endTime: null
                };
            }
            this.showNotification('Returned to main menu', 'info');
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new QuestionGeneratorApp();
});
