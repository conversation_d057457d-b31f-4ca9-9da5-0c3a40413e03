// API Key Manager JavaScript

let currentKeyInfo = null;
let keyVisible = false;

// Initialize the page
document.addEventListener('DOMContentLoaded', async () => {
    await loadCurrentKeyInfo();
});

// Load current API key information
async function loadCurrentKeyInfo() {
    try {
        const result = await window.electronAPI.getApiKeyInfo();
        currentKeyInfo = result;
        
        const container = document.getElementById('currentKeyInfo');
        
        if (result.success && result.hasKey) {
            container.innerHTML = `
                <div class="info-grid">
                    <div class="info-label">Status:</div>
                    <div class="info-value">
                        <span class="status-indicator ${result.isValidFormat ? 'status-working' : 'status-error'}"></span>
                        ${result.isValidFormat ? 'Valid Format' : 'Invalid Format'}
                    </div>
                    
                    <div class="info-label">Provider:</div>
                    <div class="info-value">${result.provider}</div>
                    
                    <div class="info-label">Key (Masked):</div>
                    <div class="info-value">${result.maskedKey}</div>
                    
                    <div class="info-label">Length:</div>
                    <div class="info-value">${result.keyLength} characters</div>
                </div>
            `;
            
            if (!result.isValidFormat) {
                showAlert('warning', 'Your current API key format is invalid. Please update it with a valid OpenRouter key.');
            }
        } else {
            container.innerHTML = `
                <div class="alert error">
                    <strong>No API Key Found</strong><br>
                    Please add an OpenRouter API key to use the question generation features.
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading API key info:', error);
        document.getElementById('currentKeyInfo').innerHTML = `
            <div class="alert error">
                <strong>Error:</strong> ${error.message}
            </div>
        `;
    }
}

// Update API key
async function updateApiKey() {
    const newKey = document.getElementById('newApiKey').value.trim();
    const updateButton = document.getElementById('updateButton');
    
    if (!newKey) {
        showAlert('error', 'Please enter an API key.');
        return;
    }
    
    // Validate format
    if (!newKey.startsWith('sk-or-v1-')) {
        showAlert('error', 'API key must start with "sk-or-v1-"');
        return;
    }
    
    if (newKey.length !== 73) {
        showAlert('error', 'API key must be exactly 73 characters long.');
        return;
    }
    
    // Show loading state
    updateButton.disabled = true;
    updateButton.innerHTML = '<span class="loading"></span>Updating...';
    
    try {
        const result = await window.electronAPI.updateApiKey(newKey);
        
        if (result.success) {
            showAlert('success', result.message);
            document.getElementById('newApiKey').value = '';
            await loadCurrentKeyInfo(); // Refresh the current key info
        } else {
            showAlert('error', result.error);
        }
    } catch (error) {
        console.error('Error updating API key:', error);
        showAlert('error', `Failed to update API key: ${error.message}`);
    } finally {
        updateButton.disabled = false;
        updateButton.innerHTML = 'Update API Key';
    }
}

// Test current API key
async function testCurrentKey() {
    const testButton = document.getElementById('testButton');
    const testResults = document.getElementById('testResults');
    
    // Show loading state
    testButton.disabled = true;
    testButton.innerHTML = '<span class="loading"></span>Testing...';
    testResults.className = 'hidden';
    
    try {
        const result = await window.electronAPI.testApiKey();
        
        testResults.className = '';
        
        if (result.success) {
            testResults.innerHTML = `
                <div class="alert success">
                    <strong>✅ API Key Test Successful!</strong><br>
                    ${result.message}<br>
                    Questions generated: ${result.questionsGenerated}
                </div>
            `;
        } else {
            testResults.innerHTML = `
                <div class="alert error">
                    <strong>❌ API Key Test Failed</strong><br>
                    ${result.error}
                </div>
            `;
        }
    } catch (error) {
        console.error('Error testing API key:', error);
        testResults.className = '';
        testResults.innerHTML = `
            <div class="alert error">
                <strong>❌ Test Error</strong><br>
                ${error.message}
            </div>
        `;
    } finally {
        testButton.disabled = false;
        testButton.innerHTML = 'Test Current Key';
    }
}

// Toggle key visibility
function toggleKeyVisibility() {
    const input = document.getElementById('newApiKey');
    const button = document.getElementById('toggleButton');
    
    if (keyVisible) {
        input.type = 'password';
        button.textContent = 'Show Key';
        keyVisible = false;
    } else {
        input.type = 'text';
        button.textContent = 'Hide Key';
        keyVisible = true;
    }
}

// Show alert message
function showAlert(type, message) {
    const container = document.getElementById('alertContainer');
    const alertId = 'alert-' + Date.now();
    
    const alertHtml = `
        <div id="${alertId}" class="alert ${type}">
            ${message}
        </div>
    `;
    
    container.innerHTML = alertHtml;
    
    // Auto-hide success messages after 5 seconds
    if (type === 'success') {
        setTimeout(() => {
            const alertElement = document.getElementById(alertId);
            if (alertElement) {
                alertElement.remove();
            }
        }, 5000);
    }
}

// Go back to main application
function goBack() {
    if (window.electronAPI && window.electronAPI.closeWindow) {
        window.electronAPI.closeWindow();
    } else {
        window.close();
    }
}

// Handle Enter key in input field
document.getElementById('newApiKey').addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
        updateApiKey();
    }
});

// Auto-format API key input
document.getElementById('newApiKey').addEventListener('input', (e) => {
    const value = e.target.value;
    
    // Visual feedback for format validation
    if (value.length === 0) {
        e.target.style.borderColor = '#cbd5e0';
    } else if (value.startsWith('sk-or-v1-') && value.length === 73) {
        e.target.style.borderColor = '#48bb78'; // Green for valid
    } else {
        e.target.style.borderColor = '#f56565'; // Red for invalid
    }
});
