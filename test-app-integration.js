const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');

// Import the IPC handlers
require('./src/ipcHandlers');

let mainWindow;

function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'src', 'preload.js')
        },
        show: false
    });
    mainWindow.loadFile('src/renderer/index.html');
}

async function testAppIntegration() {
    console.log('\n🧪 APPLICATION INTEGRATION TEST');
    console.log('🎯 Testing the actual IPC handlers that the app uses');
    console.log('🔍 Verifying end-to-end question generation\n');
    
    try {
        console.log('📋 Test Parameters:');
        console.log('   Content: "The human heart has four chambers."');
        console.log('   Type: MCQ');
        console.log('   Count: 2');
        console.log('   Model: deepseek/deepseek-chat-v3-0324:free');
        
        console.log('\n🚀 Testing IPC handler directly...');
        
        const startTime = Date.now();
        
        // Simulate the IPC call that the frontend makes
        const mockEvent = { sender: { send: () => {} } };
        
        // Call the actual IPC handler
        const result = await new Promise((resolve, reject) => {
            // Find the generate-questions handler
            const handlers = ipcMain._events['generate-questions'] || [];
            const handler = handlers.find(h => typeof h === 'function') || handlers[0];
            
            if (!handler) {
                reject(new Error('No generate-questions handler found'));
                return;
            }
            
            // Call the handler
            handler(mockEvent, 
                "The human heart has four chambers. It consists of two atria and two ventricles.",
                "MCQ",
                2,
                'deepseek/deepseek-chat-v3-0324:free'
            ).then(resolve).catch(reject);
        });
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        console.log(`⏱️  IPC handler completed in ${duration}ms`);
        console.log('\n📊 RESULT ANALYSIS:');
        console.log('   Result type:', typeof result);
        console.log('   Is array:', Array.isArray(result));
        console.log('   Length:', result ? result.length : 'N/A');
        
        if (result && Array.isArray(result) && result.length > 0) {
            console.log('\n✅ SUCCESS! IPC handler returned questions:');
            result.forEach((question, index) => {
                console.log(`\n   Question ${index + 1}:`);
                console.log(`   • Text: "${question.question}"`);
                console.log(`   • Type: ${question.type || 'MCQ'}`);
                if (question.options) {
                    console.log(`   • Options: ${question.options.join(', ')}`);
                }
                console.log(`   • Answer: ${question.answer}`);
                console.log(`   • Explanation: "${question.explanation}"`);
            });
            
            console.log('\n🎉 APPLICATION INTEGRATION SUCCESSFUL!');
            console.log('✅ The main application is working correctly');
            console.log('✅ IPC handlers are functioning properly');
            console.log('✅ Question generation is working end-to-end');
            console.log('✅ Users can now generate questions in the app');
            
        } else {
            console.log('\n❌ IPC HANDLER FAILED');
            console.log('   Result:', result);
        }
        
    } catch (error) {
        console.log('\n❌ APPLICATION INTEGRATION FAILED:');
        console.log('   Error message:', error.message);
        console.log('   Error stack:', error.stack);
    }
    
    console.log('\n🎯 FINAL STATUS:');
    console.log('If successful: The application is ready for use');
    console.log('If failed: There may be an issue with the IPC integration');
    
    setTimeout(() => {
        console.log('\n🏁 Integration test complete. Exiting...\n');
        app.quit();
    }, 3000);
}

app.whenReady().then(() => {
    createWindow();
    setTimeout(testAppIntegration, 3000);
});

app.on('window-all-closed', () => {
    app.quit();
});

process.on('SIGINT', () => {
    console.log('\n🛑 Integration test interrupted by user');
    app.quit();
});
