<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Key Manager</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #4a5568;
            margin-bottom: 10px;
            font-size: 2.5em;
        }

        .header p {
            color: #718096;
            font-size: 1.1em;
        }

        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            background: #f7fafc;
        }

        .section h2 {
            color: #2d3748;
            margin-bottom: 15px;
            font-size: 1.5em;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 10px;
            margin-bottom: 20px;
        }

        .info-label {
            font-weight: bold;
            color: #4a5568;
        }

        .info-value {
            color: #2d3748;
            font-family: 'Courier New', monospace;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-working { background-color: #48bb78; }
        .status-error { background-color: #f56565; }
        .status-unknown { background-color: #ed8936; }

        .input-group {
            margin-bottom: 20px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #4a5568;
        }

        .input-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #cbd5e0;
            border-radius: 8px;
            font-size: 14px;
            font-family: 'Courier New', monospace;
            box-sizing: border-box;
        }

        .input-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: transform 0.2s;
        }

        .button:hover {
            transform: translateY(-2px);
        }

        .button:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
        }

        .button.secondary {
            background: #718096;
        }

        .button.danger {
            background: #f56565;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .alert.success {
            background: #c6f6d5;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }

        .alert.error {
            background: #fed7d7;
            color: #742a2a;
            border: 1px solid #fc8181;
        }

        .alert.warning {
            background: #fefcbf;
            color: #744210;
            border: 1px solid #f6e05e;
        }

        .alert.info {
            background: #bee3f8;
            color: #2a4365;
            border: 1px solid #90cdf4;
        }

        .hidden {
            display: none;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .back-button {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            color: #4a5568;
            transition: background 0.2s;
        }

        .back-button:hover {
            background: white;
        }

        .help-text {
            font-size: 0.9em;
            color: #718096;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="goBack()">← Back to App</button>
    
    <div class="container">
        <div class="header">
            <h1>🔑 API Key Manager</h1>
            <p>Manage your OpenRouter API key for question generation</p>
        </div>

        <div id="alertContainer"></div>

        <!-- Current API Key Status -->
        <div class="section">
            <h2>Current API Key Status</h2>
            <div id="currentKeyInfo">
                <div class="loading"></div>
                Loading API key information...
            </div>
        </div>

        <!-- Update API Key -->
        <div class="section">
            <h2>Update API Key</h2>
            <div class="input-group">
                <label for="newApiKey">New OpenRouter API Key:</label>
                <input type="password" id="newApiKey" placeholder="sk-or-v1-..." maxlength="73">
                <div class="help-text">
                    Enter your new OpenRouter API key. It should start with "sk-or-v1-" and be 73 characters long.
                </div>
            </div>
            <button class="button" onclick="updateApiKey()" id="updateButton">
                Update API Key
            </button>
            <button class="button secondary" onclick="toggleKeyVisibility()" id="toggleButton">
                Show Key
            </button>
        </div>

        <!-- Test API Key -->
        <div class="section">
            <h2>Test API Key</h2>
            <p>Test your current API key by generating a sample question:</p>
            <button class="button" onclick="testCurrentKey()" id="testButton">
                Test Current Key
            </button>
            <div id="testResults" class="hidden"></div>
        </div>

        <!-- Help & Instructions -->
        <div class="section">
            <h2>📚 Instructions</h2>
            <ol>
                <li><strong>Get an API Key:</strong> Visit <a href="https://openrouter.ai/" target="_blank">OpenRouter.ai</a> and create an account</li>
                <li><strong>Generate Key:</strong> Go to Settings → API Keys and create a new key</li>
                <li><strong>Enable Privacy:</strong> Go to Settings → Privacy and enable "Prompt Training"</li>
                <li><strong>Update Here:</strong> Paste your new key above and click "Update API Key"</li>
                <li><strong>Test:</strong> Use the test button to verify your key works</li>
                <li><strong>Restart:</strong> Restart the application for full effect</li>
            </ol>
        </div>
    </div>

    <script src="api-key-manager.js"></script>
</body>
</html>
