const { app, BrowserWindow } = require('electron');
const path = require('path');
const fs = require('fs');

// Import the IPC handlers and services
require('./src/ipcHandlers');
const apiService = require('./src/services/apiService');

let mainWindow;
let successfulModels = [];

function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'src', 'preload.js')
        },
        show: false
    });
    mainWindow.loadFile('src/renderer/index.html');
}

async function runVerificationSimulation() {
    console.log('\n🎉 VERIFICATION SIMULATION');
    console.log('🔧 Testing after enabling OpenRouter privacy settings');
    console.log('🎯 Checking if models are now working!\n');
    
    const testParams = {
        content: "The human heart has four chambers: two atria and two ventricles. Blood flows from the right atrium to the right ventricle, then to the lungs for oxygenation.",
        questionType: "MCQ",
        questionCount: 3
    };
    
    console.log('📋 Test Parameters:');
    console.log(`   Content: ${testParams.content.substring(0, 80)}...`);
    console.log(`   Question Type: ${testParams.questionType}`);
    console.log(`   Question Count: ${testParams.questionCount}`);
    console.log('\n🚀 Testing all models after privacy fix...\n');
    
    try {
        // Test the three main models you wanted
        const priorityModels = [
            { id: 'deepseek/deepseek-chat-v3-0324:free', name: 'DeepSeek Chat V3 (Latest)', custom: false },
            { id: 'google/gemini-2.0-flash-exp:free', name: 'Gemini 2.0 Flash (Fast)', custom: false },
            { id: 'mistralai/devstral-small:free', name: 'Mistral Devstral Small (Code)', custom: false }
        ];
        
        console.log('🎯 Testing Priority Models First:\n');
        
        for (let i = 0; i < priorityModels.length; i++) {
            const model = priorityModels[i];
            console.log(`[${i + 1}/3] Testing: ${model.name}`);
            console.log(`    Model ID: ${model.id}`);
            
            const startTime = Date.now();
            
            try {
                // Test the model - generateQuestionsFromAPI returns array directly, not {success, questions}
                const questions = await apiService.generateQuestionsFromAPI(
                    testParams.content,
                    testParams.questionType,
                    testParams.questionCount,
                    2, // retries
                    false, // isScanned
                    'verification-user',
                    'text', // contentType
                    model.id // preferredModel
                );

                const endTime = Date.now();
                const duration = endTime - startTime;

                if (questions && Array.isArray(questions) && questions.length > 0) {
                    console.log(`    ✅ SUCCESS: ${questions.length} questions generated in ${duration}ms`);

                    // Show first question as proof
                    if (questions[0]) {
                        console.log(`    📝 Sample Question: "${questions[0].question}"`);
                        console.log(`    📝 Question Type: ${questions[0].type || testParams.questionType}`);
                        if (questions[0].options) {
                            console.log(`    📝 Options: ${questions[0].options.join(', ')}`);
                        }
                        console.log(`    📝 Answer: ${questions[0].answer}`);
                    }

                    successfulModels.push({
                        model: model,
                        success: true,
                        duration: duration,
                        questionsGenerated: questions.length,
                        questions: questions
                    });

                    console.log(`    🎊 MODEL IS WORKING!`);

                } else {
                    console.log(`    ❌ FAILED: No questions generated (${duration}ms)`);
                }
                
            } catch (error) {
                const endTime = Date.now();
                const duration = endTime - startTime;
                console.log(`    ⚠️  ERROR: ${error.message} (${duration}ms)`);
            }
            
            console.log('');
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
        // Test additional models if priority ones work
        if (successfulModels.length > 0) {
            console.log('\n🎯 Testing Additional Models:\n');
            
            const additionalModels = [
                { id: 'google/gemma-3-27b-it:free', name: 'Gemma 3 27B (Balanced)', custom: false },
                { id: 'nvidia/llama-3.1-nemotron-ultra-253b-v1:free', name: 'Llama 3.1 Nemotron (Powerful)', custom: false },
                { id: 'qwen/qwen2.5-vl-72b-instruct:free', name: 'Qwen 2.5 VL (Vision)', custom: false }
            ];
            
            for (let i = 0; i < additionalModels.length; i++) {
                const model = additionalModels[i];
                console.log(`[${i + 1}/3] Testing: ${model.name}`);
                
                try {
                    const questions = await apiService.generateQuestionsFromAPI(
                        "Quick test content.",
                        "TF",
                        1,
                        2, // retries
                        false, // isScanned
                        'additional-test',
                        'text', // contentType
                        model.id // preferredModel
                    );

                    if (questions && Array.isArray(questions) && questions.length > 0) {
                        console.log(`    ✅ SUCCESS: ${questions.length} questions`);
                        successfulModels.push({
                            model: model,
                            success: true,
                            questionsGenerated: questions.length
                        });
                    } else {
                        console.log(`    ❌ FAILED: No questions generated`);
                    }
                    
                } catch (error) {
                    console.log(`    ⚠️  ERROR: ${error.message}`);
                }
                
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        
        // Generate final summary
        console.log('\n📊 VERIFICATION RESULTS:');
        console.log('═'.repeat(60));
        console.log(`🔧 Privacy Settings: ENABLED`);
        console.log(`🎯 Total Working Models: ${successfulModels.length}`);
        console.log(`📈 Success Rate: ${successfulModels.length > 0 ? 'EXCELLENT' : 'NEEDS INVESTIGATION'}`);
        console.log('═'.repeat(60));
        
        if (successfulModels.length > 0) {
            console.log('\n🎉 WORKING MODELS CONFIRMED:');
            successfulModels.forEach((result, index) => {
                console.log(`   ${index + 1}. ✅ ${result.model.name}`);
                console.log(`      • Model ID: ${result.model.id}`);
                console.log(`      • Questions Generated: ${result.questionsGenerated}`);
                if (result.duration) {
                    console.log(`      • Response Time: ${result.duration}ms`);
                }
            });
            
            console.log('\n🎯 RECOMMENDATIONS:');
            console.log('   ✅ Privacy settings fix was successful!');
            console.log('   ✅ Models are now working properly');
            console.log('   ✅ You can now use these models for question generation');
            console.log('   ✅ The application should work normally');
            
        } else {
            console.log('\n😞 NO WORKING MODELS FOUND');
            console.log('🔍 Privacy settings may need more time to take effect');
            console.log('💡 Try again in a few minutes');
        }
        
    } catch (error) {
        console.error('❌ Verification simulation failed:', error.message);
    }
    
    setTimeout(() => {
        console.log('\n🏁 Verification simulation complete. Exiting...\n');
        app.quit();
    }, 3000);
}

app.whenReady().then(() => {
    createWindow();
    setTimeout(runVerificationSimulation, 3000);
});

app.on('window-all-closed', () => {
    app.quit();
});

process.on('SIGINT', () => {
    console.log('\n🛑 Verification interrupted by user');
    app.quit();
});
