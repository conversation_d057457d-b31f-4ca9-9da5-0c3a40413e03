const { app, BrowserWindow } = require('electron');
const path = require('path');

// Import the IPC handlers and services
require('./src/ipcHandlers');
const apiService = require('./src/services/apiService');

let mainWindow;

function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'src', 'preload.js')
        },
        show: false
    });
    mainWindow.loadFile('src/renderer/index.html');
}

async function runDirectTest() {
    console.log('\n🔬 DIRECT API TEST');
    console.log('🎯 Testing generateQuestionsFromAPI directly');
    console.log('🔍 Checking if the function returns questions\n');
    
    try {
        console.log('📋 Test Parameters:');
        console.log('   Content: "Water is wet."');
        console.log('   Type: TF');
        console.log('   Count: 1');
        console.log('   Model: deepseek/deepseek-chat-v3-0324:free');
        
        console.log('\n🚀 Calling generateQuestionsFromAPI directly...');
        
        const startTime = Date.now();
        
        // Call the function with exact parameters
        const result = await apiService.generateQuestionsFromAPI(
            "Water is wet.",           // text
            "TF",                      // type
            1,                         // count
            0,                         // retries
            false,                     // isScanned
            'direct-test',             // userId
            'text',                    // contentType
            'deepseek/deepseek-chat-v3-0324:free' // preferredModel
        );
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        console.log(`⏱️  Function completed in ${duration}ms`);
        console.log('\n📊 RESULT ANALYSIS:');
        console.log('   Result type:', typeof result);
        console.log('   Is array:', Array.isArray(result));
        console.log('   Length:', result ? result.length : 'N/A');
        
        if (result && Array.isArray(result) && result.length > 0) {
            console.log('\n✅ SUCCESS! Function returned questions:');
            result.forEach((question, index) => {
                console.log(`\n   Question ${index + 1}:`);
                console.log(`   • Text: "${question.question}"`);
                console.log(`   • Answer: ${question.answer}`);
                console.log(`   • Explanation: "${question.explanation}"`);
                if (question.options) {
                    console.log(`   • Options: ${question.options.join(', ')}`);
                }
            });
            
            console.log('\n🎉 DIRECT TEST SUCCESSFUL!');
            console.log('✅ The function IS working and returning questions');
            console.log('✅ The issue must be in the wrapper or calling code');
            
        } else if (result === null || result === undefined) {
            console.log('\n❌ FUNCTION RETURNED NULL/UNDEFINED');
            console.log('🔍 This suggests an exception was thrown and caught');
            
        } else {
            console.log('\n❌ UNEXPECTED RESULT FORMAT');
            console.log('   Result:', result);
        }
        
    } catch (error) {
        console.log('\n❌ FUNCTION THREW EXCEPTION:');
        console.log('   Error message:', error.message);
        console.log('   Error stack:', error.stack);
        
        console.log('\n🔍 EXCEPTION ANALYSIS:');
        if (error.message.includes('rate limit')) {
            console.log('   • This is a rate limit error');
        } else if (error.message.includes('parse')) {
            console.log('   • This is a parsing error');
        } else if (error.message.includes('Failed to generate questions')) {
            console.log('   • This is the final error thrown when all parsing fails');
        } else {
            console.log('   • This is an unexpected error type');
        }
    }
    
    console.log('\n🎯 CONCLUSION:');
    console.log('If the function returned questions: The issue is in the wrapper code');
    console.log('If the function threw an exception: The issue is in the parsing logic');
    console.log('If the function returned null: There\'s a silent failure somewhere');
    
    setTimeout(() => {
        console.log('\n🏁 Direct test complete. Exiting...\n');
        app.quit();
    }, 3000);
}

app.whenReady().then(() => {
    createWindow();
    setTimeout(runDirectTest, 3000);
});

app.on('window-all-closed', () => {
    app.quit();
});

process.on('SIGINT', () => {
    console.log('\n🛑 Direct test interrupted by user');
    app.quit();
});
