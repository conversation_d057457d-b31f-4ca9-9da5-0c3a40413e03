const { app, BrowserWindow } = require('electron');
const path = require('path');
const fs = require('fs');

// Import the IPC handlers and services
require('./src/ipcHandlers');
const apiService = require('./src/services/apiService');

let mainWindow;
let simulationRunning = false;
let successfulModels = [];

function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'src', 'preload.js')
        },
        show: false
    });
    mainWindow.loadFile('src/renderer/index.html');
}

async function runNewKeySimulation() {
    console.log('\n🔑 NEW API KEY SIMULATION');
    console.log('🎯 Testing with fresh API key: 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');
    console.log('🚀 Attempting to bypass rate limits with new key\n');
    
    simulationRunning = true;
    
    const testParams = {
        content: "The human heart has four chambers: two atria and two ventricles. Blood flows from the right atrium to the right ventricle, then to the lungs for oxygenation.",
        questionType: "MCQ",
        questionCount: 3
    };
    
    console.log('📋 Test Parameters:');
    console.log(`   Content: ${testParams.content.substring(0, 80)}...`);
    console.log(`   Question Type: ${testParams.questionType}`);
    console.log(`   Question Count: ${testParams.questionCount}`);
    console.log('\n🔄 Testing all models with new API key...\n');
    
    try {
        // Get all available models
        const models = [
            { id: 'deepseek/deepseek-chat-v3-0324:free', name: 'DeepSeek Chat V3 (Latest)', custom: false },
            { id: 'google/gemini-2.0-flash-exp:free', name: 'Gemini 2.0 Flash (Fast)', custom: false },
            { id: 'mistralai/devstral-small:free', name: 'Mistral Devstral Small (Code)', custom: false },
            { id: 'google/gemma-3-27b-it:free', name: 'Gemma 3 27B (Balanced)', custom: false },
            { id: 'google/gemini-2.0-flash-thinking-exp:free', name: 'Gemini 2.0 Thinking (Advanced)', custom: false },
            { id: 'nvidia/llama-3.1-nemotron-ultra-253b-v1:free', name: 'Llama 3.1 Nemotron (Powerful)', custom: false },
            { id: 'deepseek/deepseek-r1-distill-llama-70b:free', name: 'DeepSeek R1 (Analytical)', custom: false },
            { id: 'qwen/qwen2.5-vl-72b-instruct:free', name: 'Qwen 2.5 VL (Vision)', custom: false },
            { id: 'meta-llama/llama-3.3-70b-instruct:free', name: 'Llama 3.3 70B (Reliable)', custom: false }
        ];

        // Read removed models list
        const removedModelsPath = path.join(__dirname, 'src', 'config', 'removed-models.json');
        let removedModels = [];
        
        if (fs.existsSync(removedModelsPath)) {
            const removedData = fs.readFileSync(removedModelsPath, 'utf8');
            removedModels = JSON.parse(removedData);
        }
        
        // Filter out removed models
        const availableModels = models.filter(model => 
            !removedModels.includes(model.id)
        );

        console.log(`🎯 Testing ${availableModels.length} available models:\n`);
        
        const results = [];
        
        // Test each model
        for (let i = 0; i < availableModels.length && simulationRunning; i++) {
            const model = availableModels[i];
            console.log(`[${i + 1}/${availableModels.length}] Testing: ${model.name} (${model.id})`);
            
            const startTime = Date.now();
            
            try {
                // Test the model with the new API key
                const result = await apiService.generateQuestionsFromAPI(
                    testParams.content,
                    testParams.questionType,
                    testParams.questionCount,
                    'new-key-user',
                    model.id // Force specific model
                );
                
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                if (result.success && result.questions && result.questions.length > 0) {
                    console.log(`   ✅ SUCCESS: ${result.questions.length} questions generated in ${duration}ms`);
                    
                    // Show first question as proof
                    if (result.questions[0]) {
                        console.log(`   📝 Sample: "${result.questions[0].question.substring(0, 80)}..."`);
                    }
                    
                    const successfulModel = {
                        model: model,
                        success: true,
                        duration: duration,
                        questionsGenerated: result.questions.length,
                        questions: result.questions
                    };
                    
                    successfulModels.push(successfulModel);
                    results.push(successfulModel);
                    
                    console.log(`   🎊 WORKING MODEL FOUND!`);
                    
                } else {
                    console.log(`   ❌ FAILED: ${result.error || 'No questions generated'} (${duration}ms)`);
                    results.push({
                        model: model,
                        success: false,
                        duration: duration,
                        error: result.error || 'No questions generated'
                    });
                }
                
            } catch (error) {
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                console.log(`   ⚠️  ERROR: ${error.message} (${duration}ms)`);
                results.push({
                    model: model,
                    success: false,
                    duration: duration,
                    error: error.message
                });
            }
            
            // Small delay between tests
            await new Promise(resolve => setTimeout(resolve, 1500));
            console.log('');
        }
        
        // Generate summary
        const successCount = successfulModels.length;
        const totalCount = results.length;
        const avgDuration = results.length > 0 ? Math.round(
            results.reduce((sum, r) => sum + r.duration, 0) / results.length
        ) : 0;
        
        console.log('📊 NEW API KEY SIMULATION RESULTS:');
        console.log('═'.repeat(60));
        console.log(`🔑 API Key: 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`);
        console.log(`📈 Total Models Tested: ${totalCount}`);
        console.log(`✅ Successful Models: ${successCount}`);
        console.log(`❌ Failed Models: ${totalCount - successCount}`);
        console.log(`⏱️  Average Response Time: ${avgDuration}ms`);
        console.log(`📊 Success Rate: ${totalCount > 0 ? Math.round((successCount / totalCount) * 100) : 0}%`);
        console.log('═'.repeat(60));
        
        if (successCount > 0) {
            console.log('\n🎉 WORKING MODELS WITH NEW API KEY:');
            successfulModels.forEach(result => {
                console.log(`   ✅ ${result.model.name}`);
                console.log(`      • Questions: ${result.questionsGenerated}`);
                console.log(`      • Response Time: ${result.duration}ms`);
                console.log(`      • Model ID: ${result.model.id}`);
            });
            
            console.log('\n🎯 RECOMMENDATION:');
            console.log('   • New API key is working!');
            console.log('   • Use these working models for question generation');
            console.log('   • Rate limits have been bypassed successfully');
        } else {
            console.log('\n😞 NO WORKING MODELS FOUND');
            console.log('🔍 All models still failing with new API key');
            console.log('💡 Possible issues:');
            console.log('   • API key may need activation time');
            console.log('   • Rate limits may be account-wide');
            console.log('   • OpenRouter service may be experiencing issues');
        }
        
    } catch (error) {
        console.error('❌ Simulation failed:', error.message);
    }
    
    simulationRunning = false;
    
    setTimeout(() => {
        console.log('\n🏁 New API key simulation complete. Exiting...\n');
        app.quit();
    }, 3000);
}

app.whenReady().then(() => {
    createWindow();
    setTimeout(runNewKeySimulation, 3000);
});

app.on('window-all-closed', () => {
    simulationRunning = false;
    app.quit();
});

process.on('SIGINT', () => {
    console.log('\n🛑 Simulation interrupted by user');
    simulationRunning = false;
    app.quit();
});
