const { app, BrowserWindow } = require('electron');
const path = require('path');
const fs = require('fs');

// Import the IPC handlers and services
require('./src/ipcHandlers');
const apiService = require('./src/services/apiService');

let mainWindow;
let simulationRunning = false;
let successfulModels = [];
let attemptCount = 0;
const maxAttempts = 50; // Maximum attempts before giving up

function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'src', 'preload.js')
        },
        show: false // Don't show window for automated testing
    });

    mainWindow.loadFile('src/renderer/index.html');
}

async function runPersistentSimulation() {
    console.log('\n🔄 PERSISTENT MODEL SIMULATION STARTED');
    console.log('🎯 Goal: Find working models using multiple approaches');
    console.log('⏰ Will keep testing until successful models are found\n');
    
    simulationRunning = true;
    
    const testParams = {
        content: "The human heart has four chambers: two atria and two ventricles. Blood flows from the right atrium to the right ventricle, then to the lungs for oxygenation.",
        questionType: "MCQ",
        questionCount: 2 // Reduced for faster testing
    };
    
    // Different test approaches to try
    const approaches = [
        { name: "Standard Test", delay: 1000 },
        { name: "Quick Test", delay: 500 },
        { name: "Slow Test", delay: 3000 },
        { name: "Minimal Test", questionCount: 1, delay: 1000 },
        { name: "Extended Test", questionCount: 5, delay: 2000 }
    ];
    
    while (simulationRunning && attemptCount < maxAttempts && successfulModels.length === 0) {
        attemptCount++;
        const approach = approaches[(attemptCount - 1) % approaches.length];
        
        console.log(`\n🔬 ATTEMPT ${attemptCount}/${maxAttempts} - ${approach.name}`);
        console.log(`⏱️  Delay: ${approach.delay}ms, Questions: ${approach.questionCount || testParams.questionCount}`);
        console.log('═'.repeat(60));
        
        const currentTestParams = {
            ...testParams,
            questionCount: approach.questionCount || testParams.questionCount
        };
        
        await runSingleSimulation(currentTestParams, approach.delay);
        
        if (successfulModels.length > 0) {
            console.log('\n🎉 SUCCESS! Found working models!');
            break;
        }
        
        // Wait before next attempt
        const waitTime = 5000 + (attemptCount * 1000); // Increasing wait time
        console.log(`\n⏳ Waiting ${waitTime/1000}s before next attempt...\n`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    
    if (successfulModels.length > 0) {
        console.log('\n🎊 SIMULATION SUCCESSFUL!');
        console.log('✅ Working models found:');
        successfulModels.forEach(model => {
            console.log(`   • ${model.name}: ${model.questionsGenerated} questions in ${model.duration}ms`);
        });
    } else {
        console.log('\n😞 SIMULATION COMPLETED WITHOUT SUCCESS');
        console.log(`❌ Tested ${attemptCount} times with different approaches`);
        console.log('💡 Suggestions:');
        console.log('   • Try again later when rate limits reset');
        console.log('   • Check API key status');
        console.log('   • Consider upgrading to paid tier');
    }
    
    simulationRunning = false;
    
    // Exit after a delay
    setTimeout(() => {
        console.log('\n🏁 Exiting simulation...\n');
        app.quit();
    }, 3000);
}

async function runSingleSimulation(testParams, delay) {
    try {
        // Get all available models
        const defaultModels = [
            { id: 'deepseek/deepseek-chat-v3-0324:free', name: 'DeepSeek Chat V3 (Latest)', custom: false },
            { id: 'google/gemini-2.0-flash-exp:free', name: 'Gemini 2.0 Flash (Fast)', custom: false },
            { id: 'mistralai/devstral-small:free', name: 'Mistral Devstral Small (Code)', custom: false },
            { id: 'google/gemma-3-27b-it:free', name: 'Gemma 3 27B (Balanced)', custom: false },
            { id: 'google/gemini-2.0-flash-thinking-exp:free', name: 'Gemini 2.0 Thinking (Advanced)', custom: false },
            { id: 'nvidia/llama-3.1-nemotron-ultra-253b-v1:free', name: 'Llama 3.1 Nemotron (Powerful)', custom: false },
            { id: 'deepseek/deepseek-r1-distill-llama-70b:free', name: 'DeepSeek R1 (Analytical)', custom: false },
            { id: 'qwen/qwen2.5-vl-72b-instruct:free', name: 'Qwen 2.5 VL (Vision)', custom: false },
            { id: 'meta-llama/llama-3.3-70b-instruct:free', name: 'Llama 3.3 70B (Reliable)', custom: false }
        ];

        // Read removed models list
        const removedModelsPath = path.join(__dirname, 'src', 'config', 'removed-models.json');
        let removedModels = [];
        
        if (fs.existsSync(removedModelsPath)) {
            const removedData = fs.readFileSync(removedModelsPath, 'utf8');
            removedModels = JSON.parse(removedData);
        }
        
        // Filter out removed models
        const availableModels = defaultModels.filter(model => 
            !removedModels.includes(model.id)
        );

        // Shuffle models for different testing order each time
        const shuffledModels = [...availableModels].sort(() => Math.random() - 0.5);
        
        console.log(`🎯 Testing ${shuffledModels.length} models in random order:`);
        shuffledModels.forEach((model, i) => {
            console.log(`   ${i + 1}. ${model.name}`);
        });
        console.log('');
        
        // Test each model
        for (let i = 0; i < shuffledModels.length && simulationRunning; i++) {
            const model = shuffledModels[i];
            console.log(`[${i + 1}/${shuffledModels.length}] Testing: ${model.name}`);
            
            const startTime = Date.now();
            
            try {
                // Test the model with forced selection
                const result = await apiService.generateQuestionsFromAPI(
                    testParams.content,
                    testParams.questionType,
                    testParams.questionCount,
                    'desktop-user',
                    model.id // Force specific model
                );
                
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                if (result.success && result.questions && result.questions.length > 0) {
                    console.log(`   ✅ SUCCESS: ${result.questions.length} questions in ${duration}ms`);
                    
                    const successfulModel = {
                        model: model,
                        success: true,
                        duration: duration,
                        questionsGenerated: result.questions.length,
                        questions: result.questions,
                        attempt: attemptCount
                    };
                    
                    successfulModels.push(successfulModel);
                    
                    // Show first question as proof
                    if (result.questions[0]) {
                        console.log(`   📝 Sample: "${result.questions[0].question.substring(0, 80)}..."`);
                    }
                    
                    // Found working model, can continue or stop here
                    console.log(`   🎊 WORKING MODEL FOUND! Continuing to test others...`);
                    
                } else {
                    console.log(`   ❌ FAILED: ${result.error || 'No questions generated'} (${duration}ms)`);
                }
                
            } catch (error) {
                const endTime = Date.now();
                const duration = endTime - startTime;
                console.log(`   ⚠️  ERROR: ${error.message} (${duration}ms)`);
            }
            
            // Dynamic delay between tests
            if (i < shuffledModels.length - 1) {
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
        
        // Summary for this attempt
        const successCount = successfulModels.length;
        console.log(`\n📊 Attempt ${attemptCount} Results: ${successCount} working models found`);
        
    } catch (error) {
        console.error(`❌ Simulation attempt ${attemptCount} failed:`, error.message);
    }
}

app.whenReady().then(() => {
    createWindow();
    
    // Start persistent simulation after app initializes
    setTimeout(runPersistentSimulation, 3000);
});

app.on('window-all-closed', () => {
    simulationRunning = false;
    app.quit();
});

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Simulation interrupted by user');
    simulationRunning = false;
    app.quit();
});
