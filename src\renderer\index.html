<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCQ & TF Question Generator</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div id="app">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <div class="logo">
                    <button id="headerBackBtn" class="btn btn-icon" title="Back to Main" style="display: none; margin-right: 10px;">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <i class="fas fa-graduation-cap"></i>
                    <h1>MCQ & TF Generator</h1>
                </div>
                <div class="header-actions">
                    <button id="themeToggle" class="btn btn-icon" title="Toggle Dark/Light Mode">
                        <i class="fas fa-moon"></i>
                    </button>

                    <button id="historyBtn" class="btn btn-icon" title="History">
                        <i class="fas fa-history"></i>
                    </button>
                    <button id="statsBtn" class="btn btn-icon" title="Statistics">
                        <i class="fas fa-chart-bar"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Welcome Screen -->
            <div id="welcomeScreen" class="screen active">
                <div class="welcome-container">
                    <div class="welcome-header">
                        <i class="fas fa-brain welcome-icon"></i>
                        <h2>Welcome to Question Generator</h2>
                        <p>Generate multiple-choice and true/false questions from your educational content</p>
                    </div>
                    
                    <div class="question-type-selection">
                        <h3>Choose Question Type</h3>
                        <div class="type-buttons">
                            <button id="mcqBtn" class="type-btn mcq-btn">
                                <i class="fas fa-list-ul"></i>
                                <span>Multiple Choice (MCQ)</span>
                                <small>Generate questions with multiple options</small>
                            </button>
                            <button id="tfBtn" class="type-btn tf-btn">
                                <i class="fas fa-check-circle"></i>
                                <span>True/False (TF)</span>
                                <small>Generate true or false questions</small>
                            </button>
                        </div>
                    </div>

                    <div class="input-methods">
                        <h3>Add Your Content</h3>
                        <div class="input-options">
                            <div class="input-option">
                                <button id="textInputBtn" class="input-btn">
                                    <i class="fas fa-keyboard"></i>
                                    <span>Type Text</span>
                                </button>
                            </div>
                            <div class="input-option">
                                <button id="fileUploadBtn" class="input-btn">
                                    <i class="fas fa-file-upload"></i>
                                    <span>Upload File</span>
                                </button>
                            </div>
                            <div class="input-option">
                                <button id="imageUploadBtn" class="input-btn">
                                    <i class="fas fa-image"></i>
                                    <span>Upload Image</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- AI Model Selection -->
                    <div class="model-settings">
                        <h3>AI Model Selection</h3>
                        <div class="setting-item">
                            <div class="setting-info">
                                <label for="modelSelect">
                                    <i class="fas fa-brain"></i>
                                    Preferred AI Model
                                </label>
                                <p class="setting-description">Choose your preferred AI model for question generation. Different models may have varying capabilities and response times.</p>
                            </div>
                            <div class="setting-control">
                                <select id="modelSelect" class="setting-select">
                                    <option value="auto">Auto (Best Available)</option>
                                    <option value="google/gemini-2.0-flash-exp:free">Gemini 2.0 Flash (Fast)</option>
                                    <option value="google/gemma-3-27b-it:free">Gemma 3 27B (Balanced)</option>
                                    <option value="google/gemini-2.0-flash-thinking-exp:free">Gemini 2.0 Thinking (Advanced)</option>
                                    <option value="nvidia/llama-3.1-nemotron-ultra-253b-v1:free">Llama 3.1 Nemotron (Powerful)</option>
                                    <option value="deepseek/deepseek-r1-distill-llama-70b:free">DeepSeek R1 (Analytical)</option>
                                    <option value="qwen/qwen2.5-vl-72b-instruct:free">Qwen 2.5 VL (Vision)</option>
                                    <option value="meta-llama/llama-3.3-70b-instruct:free">Llama 3.3 70B (Reliable)</option>
                                </select>
                                <div class="model-status" id="modelStatus">
                                    <span class="status-indicator status-unknown"></span>
                                    <span class="status-text">Checking availability...</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Model Management -->
                    <div class="model-management">
                        <h3>Model Management</h3>
                        <div class="setting-item">
                            <div class="setting-info">
                                <label>
                                    <i class="fas fa-cogs"></i>
                                    Manage AI Models
                                </label>
                                <p class="setting-description">Add new AI models or remove existing ones from your model list.</p>
                            </div>
                            <div class="setting-control">
                                <div class="model-management-buttons">
                                    <button id="addModelBtn" class="btn btn-primary btn-sm">
                                        <i class="fas fa-plus"></i> Add Model
                                    </button>
                                    <button id="removeModelBtn" class="btn btn-danger btn-sm">
                                        <i class="fas fa-trash"></i> Remove Model
                                    </button>
                                    <button id="viewModelsBtn" class="btn btn-secondary btn-sm">
                                        <i class="fas fa-list"></i> View All Models
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="question-settings">
                        <h3>Question Count Settings</h3>
                        <div class="settings-grid">
                            <div class="setting-item">
                                <div class="setting-info">
                                    <label for="fileQuestionsCount">
                                        <i class="fas fa-file-alt"></i>
                                        Questions per Page
                                    </label>
                                    <p class="setting-description">Number of questions to generate per page of multi-page documents (PDFs, Word docs, etc.)</p>
                                </div>
                                <div class="setting-control">
                                    <input type="number" id="fileQuestionsCount" min="1" max="50" value="5" class="setting-input">
                                </div>
                            </div>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <label for="imageQuestionsCount">
                                        <i class="fas fa-image"></i>
                                        Questions per Image
                                    </label>
                                    <p class="setting-description">Number of questions to generate from uploaded images</p>
                                </div>
                                <div class="setting-control">
                                    <input type="number" id="imageQuestionsCount" min="5" max="50" value="15" class="setting-input">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Input Screen -->
            <div id="contentScreen" class="screen">
                <div class="content-container">
                    <div class="screen-header">
                        <button id="backToWelcome" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back
                        </button>
                        <h2 id="contentScreenTitle">Add Content</h2>
                    </div>

                    <!-- Text Input -->
                    <div id="textInputArea" class="input-area">
                        <textarea id="textContent" placeholder="Enter your educational content here..." rows="10"></textarea>
                        <div class="input-actions">
                            <button id="generateFromText" class="btn btn-primary">
                                <i class="fas fa-magic"></i> Generate Questions
                            </button>
                        </div>
                    </div>

                    <!-- File Upload -->
                    <div id="fileUploadArea" class="input-area">
                        <div class="upload-zone" id="fileDropZone">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <p>Drag and drop your file here or click to browse</p>
                            <small>Supported: PDF, DOCX, DOC, TXT</small>
                            <input type="file" id="fileInput" accept=".pdf,.docx,.doc,.txt" hidden>
                        </div>
                        <div id="fileInfo" class="file-info hidden">
                            <div class="file-details">
                                <i class="fas fa-file"></i>
                                <span id="fileName"></span>
                                <span id="fileSize"></span>
                            </div>
                            <button id="removeFile" class="btn btn-danger btn-sm">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="input-actions">
                            <button id="generateFromFile" class="btn btn-primary" disabled>
                                <i class="fas fa-magic"></i> Generate Questions
                            </button>
                        </div>
                    </div>

                    <!-- Image Upload -->
                    <div id="imageUploadArea" class="input-area">
                        <div class="upload-zone" id="imageDropZone">
                            <i class="fas fa-image"></i>
                            <p>Drag and drop your image here or click to browse</p>
                            <small>Supported: JPG, PNG, BMP, TIFF</small>
                            <input type="file" id="imageInput" accept=".jpg,.jpeg,.png,.bmp,.tiff,.tif" hidden>
                        </div>
                        <div id="imagePreview" class="image-preview hidden">
                            <img id="previewImg" src="" alt="Preview">
                            <button id="removeImage" class="btn btn-danger btn-sm">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="input-actions">
                            <button id="generateFromImage" class="btn btn-primary" disabled>
                                <i class="fas fa-magic"></i> Generate Questions
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Processing Screen -->
            <div id="processingScreen" class="screen">
                <div class="ai-processing-container">
                    <!-- Animated Background -->
                    <div class="processing-bg">
                        <div class="neural-network">
                            <div class="node node-1"></div>
                            <div class="node node-2"></div>
                            <div class="node node-3"></div>
                            <div class="node node-4"></div>
                            <div class="node node-5"></div>
                            <div class="connection conn-1"></div>
                            <div class="connection conn-2"></div>
                            <div class="connection conn-3"></div>
                            <div class="connection conn-4"></div>
                        </div>
                    </div>

                    <!-- Main Content -->
                    <div class="processing-content">
                        <!-- AI Brain Animation -->
                        <div class="ai-brain-container">
                            <div class="brain-core">
                                <div class="brain-pulse"></div>
                                <div class="brain-icon">
                                    <i class="fas fa-brain"></i>
                                </div>
                                <div class="thinking-dots">
                                    <span class="dot dot-1"></span>
                                    <span class="dot dot-2"></span>
                                    <span class="dot dot-3"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Title and Status -->
                        <div class="processing-info">
                            <h1 class="processing-title">
                                <span class="title-gradient">AI Question Generator</span>
                            </h1>
                            <p id="processingStatus" class="processing-status">Initializing AI models...</p>
                        </div>

                        <!-- Advanced Progress Bar -->
                        <div class="progress-container">
                            <div class="progress-track">
                                <div id="progressFill" class="progress-fill">
                                    <div class="progress-glow"></div>
                                </div>
                                <div class="progress-particles">
                                    <div class="particle particle-1"></div>
                                    <div class="particle particle-2"></div>
                                    <div class="particle particle-3"></div>
                                </div>
                            </div>
                            <div class="progress-info">
                                <span id="progressText" class="progress-percentage">0%</span>
                                <span id="progressETA" class="progress-eta">Calculating...</span>
                            </div>
                        </div>

                        <!-- Processing Steps -->
                        <div class="processing-steps">
                            <div class="step step-1" id="step1">
                                <div class="step-icon"><i class="fas fa-file-text"></i></div>
                                <span class="step-text">Analyzing Content</span>
                            </div>
                            <div class="step step-2" id="step2">
                                <div class="step-icon"><i class="fas fa-cogs"></i></div>
                                <span class="step-text">Processing AI Models</span>
                            </div>
                            <div class="step step-3" id="step3">
                                <div class="step-icon"><i class="fas fa-lightbulb"></i></div>
                                <span class="step-text">Generating Questions</span>
                            </div>
                            <div class="step step-4" id="step4">
                                <div class="step-icon"><i class="fas fa-check-circle"></i></div>
                                <span class="step-text">Finalizing</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Questions Display Screen -->
            <div id="questionsScreen" class="screen">
                <div class="questions-container">
                    <div class="screen-header">
                        <div class="header-nav">
                            <button id="backToContent" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back
                            </button>
                            <button id="backToMainFromQuestions" class="btn btn-secondary btn-sm">
                                <i class="fas fa-home"></i> Main Menu
                            </button>
                        </div>
                        <h2>Generated Questions</h2>
                        <div class="question-actions">
                            <button id="startQuizBtn" class="btn btn-primary">
                                <i class="fas fa-play"></i> Start Quiz
                            </button>
                            <button id="showAnswersBtn" class="btn btn-secondary">
                                <i class="fas fa-eye"></i> Show Questions with Answers
                            </button>
                            <button id="exportQuestionsBtn" class="btn btn-secondary" style="display: none;">
                                <i class="fas fa-download"></i> Export PDF
                            </button>
                        </div>
                    </div>
                    
                    <div id="questionsDisplay" class="questions-display">
                        <!-- Questions will be dynamically inserted here -->
                    </div>
                </div>
            </div>

            <!-- Quiz Screen -->
            <div id="quizScreen" class="screen">
                <div class="quiz-container">
                    <div class="quiz-header">
                        <div class="quiz-nav">
                            <button id="backToMainFromQuiz" class="btn btn-secondary btn-sm" title="Back to Main Menu">
                                <i class="fas fa-home"></i> Main Menu
                            </button>
                        </div>
                        <div class="quiz-progress">
                            <span id="questionNumber">Question 1</span>
                            <span id="questionCount">of 15</span>
                        </div>
                        <div class="quiz-score">
                            <span>Score: </span>
                            <span id="currentScore">0/0</span>
                        </div>
                    </div>
                    
                    <div class="quiz-content">
                        <div id="quizQuestion" class="quiz-question">
                            <!-- Current question will be displayed here -->
                        </div>
                        
                        <div id="quizOptions" class="quiz-options">
                            <!-- Answer options will be displayed here -->
                        </div>
                        
                        <div id="quizFeedback" class="quiz-feedback hidden">
                            <!-- Feedback will be displayed here -->
                        </div>
                        
                        <div class="quiz-actions">
                            <button id="submitAnswer" class="btn btn-primary" disabled>
                                Submit Answer
                            </button>
                            <button id="nextQuestion" class="btn btn-secondary hidden">
                                Next Question
                            </button>
                            <button id="finishQuiz" class="btn btn-success hidden">
                                Finish Quiz
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Results Screen -->
            <div id="resultsScreen" class="screen">
                <div class="results-container">
                    <div class="results-header">
                        <i class="fas fa-trophy results-icon"></i>
                        <h2>Quiz Results</h2>
                    </div>
                    
                    <div id="resultsDisplay" class="results-display">
                        <!-- Results will be dynamically inserted here -->
                    </div>
                    
                    <div class="results-actions">
                        <button id="newQuizBtn" class="btn btn-primary">
                            <i class="fas fa-plus"></i> New Quiz
                        </button>
                        <button id="reviewAnswersBtn" class="btn btn-secondary">
                            <i class="fas fa-eye"></i> Review Answers
                        </button>
                        <button id="saveResultsBtn" class="btn btn-secondary">
                            <i class="fas fa-save"></i> Save Results
                        </button>
                    </div>
                </div>
            </div>
        </main>

        <!-- Loading Overlay -->
        <div id="loadingOverlay" class="loading-overlay hidden">
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <p>Loading...</p>
            </div>
        </div>

        <!-- History Screen -->
        <div id="historyScreen" class="screen">
            <div class="content-container">
                <div class="screen-header">
                    <div class="header-left">
                        <button id="backToMainFromHistory" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back
                        </button>
                        <button id="mainMenuFromHistory" class="btn btn-secondary">
                            <i class="fas fa-home"></i> Main Menu
                        </button>
                    </div>
                    <h2><i class="fas fa-history"></i> Quiz History</h2>
                    <div class="header-right">
                        <button id="clearHistoryBtn" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Clear History
                        </button>
                    </div>
                </div>

                <div class="history-content">
                    <div class="history-filters">
                        <div class="filter-group">
                            <label for="historyTypeFilter">Question Type:</label>
                            <select id="historyTypeFilter" class="filter-select">
                                <option value="all">All Types</option>
                                <option value="MCQ">Multiple Choice</option>
                                <option value="TF">True/False</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="historyDateFilter">Date Range:</label>
                            <select id="historyDateFilter" class="filter-select">
                                <option value="all">All Time</option>
                                <option value="today">Today</option>
                                <option value="week">This Week</option>
                                <option value="month">This Month</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <button id="refreshHistoryBtn" class="btn btn-primary">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                        </div>
                    </div>

                    <div class="history-stats-summary">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-play-circle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="totalQuizzesHistory">0</div>
                                <div class="stat-label">Total Quizzes</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-percentage"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="avgScoreHistory">0%</div>
                                <div class="stat-label">Average Score</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="avgTimeHistory">0m</div>
                                <div class="stat-label">Average Time</div>
                            </div>
                        </div>
                    </div>

                    <div class="history-list" id="historyList">
                        <div class="loading-placeholder">
                            <i class="fas fa-spinner fa-spin"></i>
                            <p>Loading quiz history...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Screen -->
        <div id="statisticsScreen" class="screen">
            <div class="content-container">
                <div class="screen-header">
                    <div class="header-left">
                        <button id="backToMainFromStats" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back
                        </button>
                        <button id="mainMenuFromStats" class="btn btn-secondary">
                            <i class="fas fa-home"></i> Main Menu
                        </button>
                    </div>
                    <h2><i class="fas fa-chart-bar"></i> Statistics</h2>
                    <div class="header-right">
                        <button id="exportStatsBtn" class="btn btn-primary">
                            <i class="fas fa-download"></i> Export Stats
                        </button>
                    </div>
                </div>

                <div class="statistics-content">
                    <div class="stats-overview">
                        <div class="stat-card large">
                            <div class="stat-icon">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="overallScore">0%</div>
                                <div class="stat-label">Overall Score</div>
                                <div class="stat-sublabel">Across all quizzes</div>
                            </div>
                        </div>
                        <div class="stat-card large">
                            <div class="stat-icon">
                                <i class="fas fa-fire"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="currentStreak">0</div>
                                <div class="stat-label">Current Streak</div>
                                <div class="stat-sublabel">Days in a row</div>
                            </div>
                        </div>
                    </div>

                    <div class="stats-grid">
                        <div class="stats-section">
                            <h3><i class="fas fa-chart-line"></i> Performance Metrics</h3>
                            <div class="stats-cards">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-question-circle"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number" id="totalQuestions">0</div>
                                        <div class="stat-label">Total Questions</div>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number" id="correctAnswers">0</div>
                                        <div class="stat-label">Correct Answers</div>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-times-circle"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number" id="incorrectAnswers">0</div>
                                        <div class="stat-label">Incorrect Answers</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="stats-section">
                            <h3><i class="fas fa-chart-pie"></i> Question Types</h3>
                            <div class="stats-cards">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-list"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number" id="mcqQuizzes">0</div>
                                        <div class="stat-label">MCQ Quizzes</div>
                                        <div class="stat-progress">
                                            <div class="progress-bar">
                                                <div class="progress-fill" id="mcqProgress"></div>
                                            </div>
                                            <span class="progress-text" id="mcqPercentage">0%</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-check"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number" id="tfQuizzes">0</div>
                                        <div class="stat-label">True/False Quizzes</div>
                                        <div class="stat-progress">
                                            <div class="progress-bar">
                                                <div class="progress-fill" id="tfProgress"></div>
                                            </div>
                                            <span class="progress-text" id="tfPercentage">0%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="stats-section">
                            <h3><i class="fas fa-calendar-alt"></i> Activity</h3>
                            <div class="stats-cards">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-calendar-day"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number" id="quizzesToday">0</div>
                                        <div class="stat-label">Quizzes Today</div>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-calendar-week"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number" id="quizzesThisWeek">0</div>
                                        <div class="stat-label">This Week</div>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-calendar"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number" id="quizzesThisMonth">0</div>
                                        <div class="stat-label">This Month</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="achievements-section">
                        <h3><i class="fas fa-medal"></i> Achievements</h3>
                        <div class="achievements-grid" id="achievementsGrid">
                            <!-- Achievements will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Floating Back Button -->
        <button id="floatingBackBtn" class="floating-back-btn" title="Back to Main Menu" style="display: none;">
            <i class="fas fa-home"></i>
        </button>

        <!-- Notification Container -->
        <div id="notificationContainer" class="notification-container"></div>

        <!-- Model Management Dialogs -->

        <!-- Add Model Dialog -->
        <div id="addModelDialog" class="modal-overlay" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-plus"></i> Add New AI Model</h3>
                    <button class="modal-close" id="closeAddModelDialog">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="newModelId">Model ID:</label>
                        <input type="text" id="newModelId" class="form-input" placeholder="e.g., openai/gpt-4:free" required>
                        <small class="form-help">Enter the full model identifier (provider/model-name:tier)</small>
                    </div>
                    <div class="form-group">
                        <label for="newModelName">Display Name:</label>
                        <input type="text" id="newModelName" class="form-input" placeholder="e.g., GPT-4 (Free)" required>
                        <small class="form-help">Friendly name to display in the dropdown</small>
                    </div>
                    <div class="form-group">
                        <label for="newModelDescription">Description (Optional):</label>
                        <input type="text" id="newModelDescription" class="form-input" placeholder="e.g., Advanced reasoning model">
                        <small class="form-help">Brief description of the model's capabilities</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="confirmAddModel" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Model
                    </button>
                    <button id="cancelAddModel" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                </div>
            </div>
        </div>

        <!-- Remove Model Dialog -->
        <div id="removeModelDialog" class="modal-overlay" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-trash"></i> Remove AI Model</h3>
                    <button class="modal-close" id="closeRemoveModelDialog">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="removeModelSelect">Select Model to Remove:</label>
                        <select id="removeModelSelect" class="form-select" required>
                            <option value="">Choose a model to remove...</option>
                        </select>
                        <small class="form-help">⚠️ Warning: This will permanently remove the model from your list</small>
                    </div>
                    <div id="removeModelInfo" class="model-info" style="display: none;">
                        <div class="info-item">
                            <strong>Model ID:</strong> <span id="removeModelId"></span>
                        </div>
                        <div class="info-item">
                            <strong>Display Name:</strong> <span id="removeModelName"></span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="confirmRemoveModel" class="btn btn-danger" disabled>
                        <i class="fas fa-trash"></i> Remove Model
                    </button>
                    <button id="cancelRemoveModel" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                </div>
            </div>
        </div>

        <!-- View Models Dialog -->
        <div id="viewModelsDialog" class="modal-overlay" style="display: none;">
            <div class="modal-content modal-large">
                <div class="modal-header">
                    <h3><i class="fas fa-list"></i> All AI Models</h3>
                    <button class="modal-close" id="closeViewModelsDialog">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="models-list" id="allModelsList">
                        <div class="loading-placeholder">
                            <i class="fas fa-spinner fa-spin"></i>
                            <p>Loading models...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="refreshModelsList" class="btn btn-primary">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                    <button id="closeViewModels" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
