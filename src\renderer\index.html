<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCQ & TF Question Generator</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div id="app">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-graduation-cap"></i>
                    <h1>MCQ & TF Generator</h1>
                </div>
                <div class="header-actions">
                    <button id="themeToggle" class="btn btn-icon" title="Toggle Dark/Light Mode">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button id="settingsBtn" class="btn btn-icon" title="Settings">
                        <i class="fas fa-cog"></i>
                    </button>
                    <button id="historyBtn" class="btn btn-icon" title="History">
                        <i class="fas fa-history"></i>
                    </button>
                    <button id="statsBtn" class="btn btn-icon" title="Statistics">
                        <i class="fas fa-chart-bar"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Welcome Screen -->
            <div id="welcomeScreen" class="screen active">
                <div class="welcome-container">
                    <div class="welcome-header">
                        <i class="fas fa-brain welcome-icon"></i>
                        <h2>Welcome to Question Generator</h2>
                        <p>Generate multiple-choice and true/false questions from your educational content</p>
                    </div>
                    
                    <div class="question-type-selection">
                        <h3>Choose Question Type</h3>
                        <div class="type-buttons">
                            <button id="mcqBtn" class="type-btn mcq-btn">
                                <i class="fas fa-list-ul"></i>
                                <span>Multiple Choice (MCQ)</span>
                                <small>Generate questions with multiple options</small>
                            </button>
                            <button id="tfBtn" class="type-btn tf-btn">
                                <i class="fas fa-check-circle"></i>
                                <span>True/False (TF)</span>
                                <small>Generate true or false questions</small>
                            </button>
                        </div>
                    </div>

                    <div class="input-methods">
                        <h3>Add Your Content</h3>
                        <div class="input-options">
                            <div class="input-option">
                                <button id="textInputBtn" class="input-btn">
                                    <i class="fas fa-keyboard"></i>
                                    <span>Type Text</span>
                                </button>
                            </div>
                            <div class="input-option">
                                <button id="fileUploadBtn" class="input-btn">
                                    <i class="fas fa-file-upload"></i>
                                    <span>Upload File</span>
                                </button>
                            </div>
                            <div class="input-option">
                                <button id="imageUploadBtn" class="input-btn">
                                    <i class="fas fa-image"></i>
                                    <span>Upload Image</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Input Screen -->
            <div id="contentScreen" class="screen">
                <div class="content-container">
                    <div class="screen-header">
                        <button id="backToWelcome" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back
                        </button>
                        <h2 id="contentScreenTitle">Add Content</h2>
                    </div>

                    <!-- Text Input -->
                    <div id="textInputArea" class="input-area">
                        <textarea id="textContent" placeholder="Enter your educational content here..." rows="10"></textarea>
                        <div class="input-actions">
                            <button id="generateFromText" class="btn btn-primary">
                                <i class="fas fa-magic"></i> Generate Questions
                            </button>
                        </div>
                    </div>

                    <!-- File Upload -->
                    <div id="fileUploadArea" class="input-area">
                        <div class="upload-zone" id="fileDropZone">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <p>Drag and drop your file here or click to browse</p>
                            <small>Supported: PDF, DOCX, DOC, TXT</small>
                            <input type="file" id="fileInput" accept=".pdf,.docx,.doc,.txt" hidden>
                        </div>
                        <div id="fileInfo" class="file-info hidden">
                            <div class="file-details">
                                <i class="fas fa-file"></i>
                                <span id="fileName"></span>
                                <span id="fileSize"></span>
                            </div>
                            <button id="removeFile" class="btn btn-danger btn-sm">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="input-actions">
                            <button id="generateFromFile" class="btn btn-primary" disabled>
                                <i class="fas fa-magic"></i> Generate Questions
                            </button>
                        </div>
                    </div>

                    <!-- Image Upload -->
                    <div id="imageUploadArea" class="input-area">
                        <div class="upload-zone" id="imageDropZone">
                            <i class="fas fa-image"></i>
                            <p>Drag and drop your image here or click to browse</p>
                            <small>Supported: JPG, PNG, BMP, TIFF</small>
                            <input type="file" id="imageInput" accept=".jpg,.jpeg,.png,.bmp,.tiff,.tif" hidden>
                        </div>
                        <div id="imagePreview" class="image-preview hidden">
                            <img id="previewImg" src="" alt="Preview">
                            <button id="removeImage" class="btn btn-danger btn-sm">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="input-actions">
                            <button id="generateFromImage" class="btn btn-primary" disabled>
                                <i class="fas fa-magic"></i> Generate Questions
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Processing Screen -->
            <div id="processingScreen" class="screen">
                <div class="processing-container">
                    <div class="processing-animation">
                        <i class="fas fa-brain fa-spin"></i>
                    </div>
                    <h2>Generating Questions</h2>
                    <p id="processingStatus">Analyzing your content...</p>
                    <div class="progress-bar">
                        <div id="progressFill" class="progress-fill"></div>
                    </div>
                    <div id="progressText" class="progress-text">0%</div>
                </div>
            </div>

            <!-- Questions Display Screen -->
            <div id="questionsScreen" class="screen">
                <div class="questions-container">
                    <div class="screen-header">
                        <div class="header-nav">
                            <button id="backToContent" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back
                            </button>
                            <button id="backToMainFromQuestions" class="btn btn-secondary btn-sm">
                                <i class="fas fa-home"></i> Main Menu
                            </button>
                        </div>
                        <h2>Generated Questions</h2>
                        <div class="question-actions">
                            <button id="startQuizBtn" class="btn btn-primary">
                                <i class="fas fa-play"></i> Start Interactive Quiz
                            </button>
                            <button id="showAnswersBtn" class="btn btn-secondary">
                                <i class="fas fa-eye"></i> Show Questions with Answers
                            </button>
                            <button id="exportQuestionsBtn" class="btn btn-secondary">
                                <i class="fas fa-download"></i> Export
                            </button>
                        </div>
                    </div>
                    
                    <div id="questionsDisplay" class="questions-display">
                        <!-- Questions will be dynamically inserted here -->
                    </div>
                </div>
            </div>

            <!-- Quiz Screen -->
            <div id="quizScreen" class="screen">
                <div class="quiz-container">
                    <div class="quiz-header">
                        <div class="quiz-nav">
                            <button id="backToMainFromQuiz" class="btn btn-secondary btn-sm" title="Back to Main Menu">
                                <i class="fas fa-home"></i> Main Menu
                            </button>
                        </div>
                        <div class="quiz-progress">
                            <span id="questionNumber">Question 1</span>
                            <span id="questionCount">of 15</span>
                        </div>
                        <div class="quiz-score">
                            <span>Score: </span>
                            <span id="currentScore">0/0</span>
                        </div>
                    </div>
                    
                    <div class="quiz-content">
                        <div id="quizQuestion" class="quiz-question">
                            <!-- Current question will be displayed here -->
                        </div>
                        
                        <div id="quizOptions" class="quiz-options">
                            <!-- Answer options will be displayed here -->
                        </div>
                        
                        <div id="quizFeedback" class="quiz-feedback hidden">
                            <!-- Feedback will be displayed here -->
                        </div>
                        
                        <div class="quiz-actions">
                            <button id="submitAnswer" class="btn btn-primary" disabled>
                                Submit Answer
                            </button>
                            <button id="nextQuestion" class="btn btn-secondary hidden">
                                Next Question
                            </button>
                            <button id="finishQuiz" class="btn btn-success hidden">
                                Finish Quiz
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Results Screen -->
            <div id="resultsScreen" class="screen">
                <div class="results-container">
                    <div class="results-header">
                        <i class="fas fa-trophy results-icon"></i>
                        <h2>Quiz Results</h2>
                    </div>

                    <div id="resultsDisplay" class="results-display">
                        <!-- Results will be dynamically inserted here -->
                    </div>

                    <div class="results-actions">
                        <button id="newQuizBtn" class="btn btn-primary">
                            <i class="fas fa-plus"></i> New Quiz
                        </button>
                        <button id="reviewAnswersBtn" class="btn btn-secondary">
                            <i class="fas fa-eye"></i> Review Answers
                        </button>
                        <button id="saveResultsBtn" class="btn btn-secondary">
                            <i class="fas fa-save"></i> Save Results
                        </button>
                    </div>
                </div>
            </div>

            <!-- Settings Screen -->
            <div id="settingsScreen" class="screen">
                <div class="settings-container">
                    <div class="settings-header">
                        <button id="backToMainFromSettings" class="btn btn-icon back-btn">
                            <i class="fas fa-arrow-left"></i>
                        </button>
                        <div class="settings-title">
                            <i class="fas fa-cog settings-icon"></i>
                            <h2>Settings</h2>
                            <p>Customize your question generation experience</p>
                        </div>
                    </div>

                    <div class="settings-content">
                        <!-- Question Generation Settings -->
                        <div class="settings-section">
                            <h3><i class="fas fa-brain"></i> Question Generation</h3>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <label for="questionsPerPage">
                                        <i class="fas fa-file-text"></i>
                                        Questions per Page
                                    </label>
                                    <p class="setting-description">Number of questions to generate from files</p>
                                </div>
                                <div class="setting-control">
                                    <input type="number" id="questionsPerPage" min="1" max="50" value="5" class="setting-input">
                                </div>
                            </div>



                            <div class="setting-item">
                                <div class="setting-info">
                                    <label for="imageQuestionsCount">
                                        <i class="fas fa-image"></i>
                                        Image Questions Count
                                    </label>
                                    <p class="setting-description">Number of questions to generate from images</p>
                                </div>
                                <div class="setting-control">
                                    <input type="number" id="imageQuestionsCount" min="5" max="50" value="15" class="setting-input">
                                </div>
                            </div>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <label for="shuffleQuestions">
                                        <i class="fas fa-random"></i>
                                        Shuffle Questions
                                    </label>
                                    <p class="setting-description">Randomize question order in quizzes</p>
                                </div>
                                <div class="setting-control">
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="shuffleQuestions">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <label for="shuffleOptions">
                                        <i class="fas fa-shuffle"></i>
                                        Shuffle Answer Options
                                    </label>
                                    <p class="setting-description">Randomize answer option order</p>
                                </div>
                                <div class="setting-control">
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="shuffleOptions">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- UI Settings -->
                        <div class="settings-section">
                            <h3><i class="fas fa-palette"></i> Interface</h3>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <label for="themeSelect">
                                        <i class="fas fa-moon"></i>
                                        Theme
                                    </label>
                                    <p class="setting-description">Choose your preferred color theme</p>
                                </div>
                                <div class="setting-control">
                                    <select id="themeSelect" class="setting-select">
                                        <option value="light">Light</option>
                                        <option value="dark">Dark</option>
                                        <option value="auto">Auto (System)</option>
                                    </select>
                                </div>
                            </div>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <label for="showExplanations">
                                        <i class="fas fa-info-circle"></i>
                                        Show Explanations
                                    </label>
                                    <p class="setting-description">Display explanations after answering questions</p>
                                </div>
                                <div class="setting-control">
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="showExplanations" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <label for="notifications">
                                        <i class="fas fa-bell"></i>
                                        Notifications
                                    </label>
                                    <p class="setting-description">Enable desktop notifications</p>
                                </div>
                                <div class="setting-control">
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="notifications" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Export Settings -->
                        <div class="settings-section">
                            <h3><i class="fas fa-download"></i> Export & Downloads</h3>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <label for="exportFormat">
                                        <i class="fas fa-file-pdf"></i>
                                        Export Format
                                    </label>
                                    <p class="setting-description">Questions will be exported as PDF documents</p>
                                </div>
                                <div class="setting-control">
                                    <div class="export-format-display">
                                        <i class="fas fa-file-pdf pdf-icon"></i>
                                        <span>PDF</span>
                                    </div>
                                </div>
                            </div>



                            <div class="setting-item">
                                <div class="setting-info">
                                    <label for="autoOpenPDF">
                                        <i class="fas fa-external-link-alt"></i>
                                        Auto-open PDF
                                    </label>
                                    <p class="setting-description">Automatically open PDF files after export</p>
                                </div>
                                <div class="setting-control">
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="autoOpenPDF" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-actions">
                        <button id="resetSettingsBtn" class="btn btn-secondary">
                            <i class="fas fa-undo"></i> Reset to Defaults
                        </button>
                        <button id="saveSettingsBtn" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Settings
                        </button>
                    </div>
                </div>
            </div>
        </main>

        <!-- Loading Overlay -->
        <div id="loadingOverlay" class="loading-overlay hidden">
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <p>Loading...</p>
            </div>
        </div>

        <!-- Notification Container -->
        <div id="notificationContainer" class="notification-container"></div>
    </div>

    <script src="app.js"></script>
</body>
</html>
