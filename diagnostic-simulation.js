const { app, BrowserWindow } = require('electron');
const path = require('path');
const fs = require('fs');

// Import the IPC handlers and services
require('./src/ipcHandlers');
const apiService = require('./src/services/apiService');

let mainWindow;

function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'src', 'preload.js')
        },
        show: false
    });
    mainWindow.loadFile('src/renderer/index.html');
}

async function runDiagnosticSimulation() {
    console.log('\n🔍 DEEP DIAGNOSTIC SIMULATION');
    console.log('🎯 Finding the REAL problem - not rate limits!');
    console.log('🔬 Investigating API service behavior\n');
    
    // Test 1: Check API service configuration
    console.log('📋 TEST 1: API Service Configuration');
    console.log('═'.repeat(50));
    
    try {
        // Check if API service exists and is properly loaded
        console.log('✅ API Service loaded:', typeof apiService);
        console.log('✅ generateQuestionsFromAPI function:', typeof apiService.generateQuestionsFromAPI);
        
        // Check environment variables
        console.log('✅ API_KEY from env:', process.env.API_KEY ? 'Present' : 'Missing');
        console.log('✅ API_KEY length:', process.env.API_KEY ? process.env.API_KEY.length : 0);
        console.log('✅ API_KEY starts with:', process.env.API_KEY ? process.env.API_KEY.substring(0, 15) + '...' : 'N/A');
        
    } catch (error) {
        console.log('❌ API Service error:', error.message);
    }
    
    console.log('\n📋 TEST 2: Direct API Call Analysis');
    console.log('═'.repeat(50));
    
    try {
        // Test with minimal parameters
        console.log('🔬 Testing minimal API call...');
        
        const testContent = "Water is wet.";
        const testType = "TF";
        const testCount = 1;
        const testUser = "diagnostic";
        const testModel = "deepseek/deepseek-chat-v3-0324:free";
        
        console.log(`📝 Content: "${testContent}"`);
        console.log(`📝 Type: ${testType}`);
        console.log(`📝 Count: ${testCount}`);
        console.log(`📝 User: ${testUser}`);
        console.log(`📝 Model: ${testModel}`);
        
        console.log('\n🚀 Making API call...');
        const startTime = Date.now();
        
        const result = await apiService.generateQuestionsFromAPI(
            testContent,
            testType,
            testCount,
            testUser,
            testModel
        );
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        console.log(`⏱️  Call completed in ${duration}ms`);
        console.log('📊 Result structure:');
        console.log('   - success:', result.success);
        console.log('   - error:', result.error);
        console.log('   - questions:', result.questions ? result.questions.length : 'null');
        console.log('   - model:', result.model);
        
        if (result.questions && result.questions.length > 0) {
            console.log('✅ FIRST QUESTION GENERATED:');
            console.log('   Question:', result.questions[0].question);
            console.log('   Type:', result.questions[0].type);
            console.log('   Options:', result.questions[0].options);
            console.log('   Answer:', result.questions[0].answer);
        }
        
        if (result.error) {
            console.log('❌ ERROR DETAILS:');
            console.log('   Error message:', result.error);
            console.log('   Error type:', typeof result.error);
        }
        
    } catch (error) {
        console.log('❌ API call failed with exception:');
        console.log('   Error:', error.message);
        console.log('   Stack:', error.stack);
    }
    
    console.log('\n📋 TEST 3: Model Selection Investigation');
    console.log('═'.repeat(50));
    
    // Test different models individually
    const modelsToTest = [
        'deepseek/deepseek-chat-v3-0324:free',
        'google/gemini-2.0-flash-exp:free',
        'mistralai/devstral-small:free'
    ];
    
    for (const modelId of modelsToTest) {
        console.log(`\n🔬 Testing model: ${modelId}`);
        
        try {
            const result = await apiService.generateQuestionsFromAPI(
                "Simple test.",
                "TF",
                1,
                'model-test',
                modelId
            );
            
            console.log(`   Result: ${result.success ? 'SUCCESS' : 'FAILED'}`);
            if (result.error) {
                console.log(`   Error: ${result.error}`);
            }
            if (result.questions) {
                console.log(`   Questions: ${result.questions.length}`);
            }
            
        } catch (error) {
            console.log(`   Exception: ${error.message}`);
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('\n📋 TEST 4: Auto-Selection Logic Investigation');
    console.log('═'.repeat(50));
    
    try {
        // Test with no preferred model (auto mode)
        console.log('🔬 Testing auto-selection mode...');
        
        const autoResult = await apiService.generateQuestionsFromAPI(
            "Auto test content.",
            "TF",
            1,
            'auto-test',
            null // No preferred model
        );
        
        console.log('📊 Auto-selection result:');
        console.log('   Success:', autoResult.success);
        console.log('   Error:', autoResult.error);
        console.log('   Questions:', autoResult.questions ? autoResult.questions.length : 'null');
        
    } catch (error) {
        console.log('❌ Auto-selection test failed:', error.message);
    }
    
    console.log('\n📋 TEST 5: HTTP Request Investigation');
    console.log('═'.repeat(50));
    
    // Check if we can access the actual HTTP client
    try {
        const axios = require('axios');
        console.log('✅ Axios available:', typeof axios);
        
        // Test direct HTTP call to OpenRouter
        console.log('🌐 Testing direct HTTP call to OpenRouter...');
        
        const response = await axios.post('https://openrouter.ai/api/v1/chat/completions', {
            model: 'deepseek/deepseek-chat-v3-0324:free',
            messages: [
                {
                    role: 'user',
                    content: 'Generate 1 true/false question about: Water is wet.'
                }
            ]
        }, {
            headers: {
                'Authorization': `Bearer ${process.env.API_KEY}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': 'http://localhost:3000',
                'X-Title': 'Question Generator'
            },
            timeout: 30000
        });
        
        console.log('✅ Direct HTTP call successful!');
        console.log('   Status:', response.status);
        console.log('   Data keys:', Object.keys(response.data));
        
        if (response.data.choices && response.data.choices[0]) {
            console.log('   Response content:', response.data.choices[0].message.content.substring(0, 200) + '...');
        }
        
    } catch (httpError) {
        console.log('❌ Direct HTTP call failed:');
        console.log('   Status:', httpError.response?.status);
        console.log('   Status Text:', httpError.response?.statusText);
        console.log('   Error:', httpError.message);
        console.log('   Response data:', httpError.response?.data);
    }
    
    console.log('\n🎯 DIAGNOSTIC COMPLETE');
    console.log('═'.repeat(50));
    console.log('Check the logs above to identify the real issue!');
    
    setTimeout(() => {
        console.log('\n🏁 Diagnostic simulation complete. Exiting...\n');
        app.quit();
    }, 3000);
}

app.whenReady().then(() => {
    createWindow();
    setTimeout(runDiagnosticSimulation, 3000);
});

app.on('window-all-closed', () => {
    app.quit();
});

process.on('SIGINT', () => {
    console.log('\n🛑 Diagnostic interrupted by user');
    app.quit();
});
