const { ipcMain, dialog, shell, Notification } = require('electron');
const path = require('path');
const fs = require('fs');

// Import existing services (adapted for desktop)
const messageHandlers = require('./handlers/messageHandlers');
const apiService = require('./services/apiService');
const fileService = require('./services/fileService');
const database = require('./database/database');
const logger = require('./utils/logger');

class IPCHandlers {
    constructor() {
        this.setupHandlers();
    }

    setupHandlers() {
        // File operations
        ipcMain.handle('select-file', async (event, options = {}) => {
            try {
                const defaultFilters = [
                    { name: 'All Supported', extensions: ['pdf', 'docx', 'doc', 'txt', 'jpg', 'jpeg', 'png', 'bmp', 'tiff', 'tif'] },
                    { name: 'Documents', extensions: ['pdf', 'docx', 'doc', 'txt'] },
                    { name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'bmp', 'tiff', 'tif'] },
                    { name: 'PDF Files', extensions: ['pdf'] },
                    { name: 'Word Documents', extensions: ['docx', 'doc'] },
                    { name: 'Text Files', extensions: ['txt'] },
                    { name: 'All Files', extensions: ['*'] }
                ];

                const result = await dialog.showOpenDialog({
                    properties: ['openFile'],
                    filters: options.filters || defaultFilters,
                    title: options.title || 'Select a file to process'
                });

                if (!result.canceled && result.filePaths.length > 0) {
                    const filePath = result.filePaths[0];
                    logger.info(`File selected: ${filePath}`);
                    return { success: true, filePath: filePath };
                }

                return { success: false, error: 'No file selected' };
            } catch (error) {
                logger.error('Error in select-file:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('process-file', async (event, fileData) => {
            try {
                // Handle different types of file data
                let filePath;

                if (typeof fileData === 'string') {
                    // Direct file path
                    filePath = fileData;
                } else if (fileData && fileData.path) {
                    // File object with path property
                    filePath = fileData.path;
                } else if (fileData && fileData.filePath) {
                    // File object with filePath property
                    filePath = fileData.filePath;
                } else {
                    throw new Error('Invalid file data provided');
                }

                logger.info(`Processing file: ${filePath}`);

                if (!filePath || filePath === 'undefined') {
                    throw new Error('File path is undefined or invalid');
                }

                // Check if file exists
                if (!fs.existsSync(filePath)) {
                    throw new Error(`File not found: ${filePath}`);
                }

                // Use existing file service to extract text
                const result = await fileService.extractTextFromDocument(filePath);

                if (result && result.text) {
                    return {
                        success: true,
                        text: result.text,
                        pageCount: result.pageCount || 1,
                        questionCount: result.questionCount || 15
                    };
                } else {
                    throw new Error('Failed to extract text from file');
                }
            } catch (error) {
                logger.error('Error processing file:', error.message);
                return { success: false, error: error.message };
            }
        });

        // Question generation
        ipcMain.handle('generate-questions', async (event, content, type, count) => {
            try {
                logger.info(`Generating ${count} ${type} questions`);
                
                // Create a mock context object similar to Telegram context
                const mockContext = {
                    from: { id: 'desktop-user' },
                    chat: { id: 'desktop-chat' },
                    reply: (message) => {
                        logger.info('Bot reply:', message);
                        return Promise.resolve();
                    },
                    telegram: {
                        editMessageText: () => Promise.resolve()
                    }
                };

                // Use existing API service to generate questions
                const questions = await apiService.generateQuestionsFromAPI(
                    content, 
                    type, 
                    count, 
                    2, // retries
                    false, // isScanned
                    'desktop-user'
                );

                if (questions && questions.length > 0) {
                    logger.success(`Generated ${questions.length} questions`);
                    return questions;
                } else {
                    throw new Error('No questions were generated');
                }
            } catch (error) {
                logger.error('Error generating questions:', error);
                throw error;
            }
        });

        // Quiz operations
        ipcMain.handle('start-quiz', async (event, questions) => {
            try {
                // Initialize quiz session in database if needed
                const quizSession = {
                    id: Date.now().toString(),
                    questions: questions,
                    startTime: new Date().toISOString(),
                    status: 'active'
                };

                // Store session (you might want to implement this in database)
                logger.info('Quiz session started');
                return { success: true, sessionId: quizSession.id };
            } catch (error) {
                logger.error('Error starting quiz:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('submit-answer', async (event, questionIndex, answer) => {
            try {
                // Process answer submission
                logger.info(`Answer submitted for question ${questionIndex}: ${answer}`);
                return { success: true };
            } catch (error) {
                logger.error('Error submitting answer:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('get-quiz-results', async (event) => {
            try {
                // Get quiz results from database
                return { success: true, results: {} };
            } catch (error) {
                logger.error('Error getting quiz results:', error);
                return { success: false, error: error.message };
            }
        });

        // Database operations
        ipcMain.handle('save-quiz-session', async (event, session) => {
            try {
                // Save quiz session to database
                const sessionData = {
                    timestamp: session.timestamp,
                    question_type: session.questionType,
                    score_correct: session.score.correct,
                    score_total: session.score.total,
                    duration: session.duration,
                    answers: JSON.stringify(session.answers),
                    questions: JSON.stringify(session.questions)
                };

                // Use existing database methods
                const db = database.getDatabase();
                const stmt = db.prepare(`
                    INSERT INTO quiz_sessions 
                    (timestamp, question_type, score_correct, score_total, duration, answers, questions)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                `);

                stmt.run(
                    sessionData.timestamp,
                    sessionData.question_type,
                    sessionData.score_correct,
                    sessionData.score_total,
                    sessionData.duration,
                    sessionData.answers,
                    sessionData.questions
                );

                logger.success('Quiz session saved to database');
                return { success: true };
            } catch (error) {
                logger.error('Error saving quiz session:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('get-quiz-history', async (event) => {
            try {
                const db = database.getDatabase();
                const stmt = db.prepare(`
                    SELECT * FROM quiz_sessions 
                    ORDER BY timestamp DESC 
                    LIMIT 50
                `);
                
                const sessions = stmt.all();
                
                // Parse JSON fields
                const parsedSessions = sessions.map(session => ({
                    ...session,
                    answers: JSON.parse(session.answers || '[]'),
                    questions: JSON.parse(session.questions || '[]')
                }));

                return { success: true, sessions: parsedSessions };
            } catch (error) {
                logger.error('Error getting quiz history:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('get-statistics', async (event) => {
            try {
                const db = database.getDatabase();
                
                // Get basic statistics
                const totalQuizzes = db.prepare('SELECT COUNT(*) as count FROM quiz_sessions').get();
                const avgScore = db.prepare('SELECT AVG(CAST(score_correct AS FLOAT) / score_total * 100) as avg FROM quiz_sessions').get();
                const totalQuestions = db.prepare('SELECT SUM(score_total) as total FROM quiz_sessions').get();
                
                const stats = {
                    totalQuizzes: totalQuizzes.count,
                    averageScore: Math.round(avgScore.avg || 0),
                    totalQuestions: totalQuestions.total || 0,
                    lastQuizDate: null
                };

                // Get last quiz date
                const lastQuiz = db.prepare('SELECT timestamp FROM quiz_sessions ORDER BY timestamp DESC LIMIT 1').get();
                if (lastQuiz) {
                    stats.lastQuizDate = lastQuiz.timestamp;
                }

                return { success: true, statistics: stats };
            } catch (error) {
                logger.error('Error getting statistics:', error);
                return { success: false, error: error.message };
            }
        });

        // Settings
        ipcMain.handle('get-settings', async (event) => {
            try {
                // Get settings from config or database
                const settings = {
                    questionsPerPage: 5,  // Questions per page for multi-page documents
                    imageQuestionsCount: 15,  // Total questions for images
                    questionType: 'MCQ',
                    autoSave: true,
                    notifications: true
                };

                return { success: true, settings };
            } catch (error) {
                logger.error('Error getting settings:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('save-settings', async (event, settings) => {
            try {
                // Save settings to config file or database
                logger.info('Settings saved:', settings);
                return { success: true };
            } catch (error) {
                logger.error('Error saving settings:', error);
                return { success: false, error: error.message };
            }
        });

        // Feedback
        ipcMain.handle('submit-feedback', async (event, feedback) => {
            try {
                // Save feedback using existing feedback service
                const feedbackService = require('./services/feedbackService');
                const feedbackData = {
                    userId: 'desktop-user',
                    username: 'Desktop User',
                    rating: feedback.rating,
                    suggestion: feedback.suggestion,
                    quizType: feedback.quizType || 'Unknown',
                    score: feedback.score || 0
                };

                const saved = await feedbackService.saveFeedback(feedbackData);
                
                if (saved) {
                    logger.success('Feedback saved successfully');
                    return { success: true };
                } else {
                    throw new Error('Failed to save feedback');
                }
            } catch (error) {
                logger.error('Error submitting feedback:', error);
                return { success: false, error: error.message };
            }
        });

        // Utility functions
        ipcMain.handle('show-notification', async (event, title, body) => {
            try {
                if (Notification.isSupported()) {
                    new Notification({ title, body }).show();
                }
                return { success: true };
            } catch (error) {
                logger.error('Error showing notification:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('open-external', async (event, url) => {
            try {
                await shell.openExternal(url);
                return { success: true };
            } catch (error) {
                logger.error('Error opening external URL:', error);
                return { success: false, error: error.message };
            }
        });

        // File save dialog
        ipcMain.handle('save-file', async (event, options) => {
            try {
                const { dialog } = require('electron');
                const result = await dialog.showSaveDialog(options);

                if (result.canceled) {
                    return { success: false, canceled: true };
                } else {
                    return { success: true, filePath: result.filePath };
                }
            } catch (error) {
                logger.error('Error showing save dialog:', error);
                return { success: false, error: error.message };
            }
        });

        // PDF generation and save
        ipcMain.handle('save-pdf', async (event, filePath, htmlContent) => {
            try {
                const fs = require('fs').promises;
                const { BrowserWindow } = require('electron');

                // Create a hidden browser window for PDF generation
                const pdfWindow = new BrowserWindow({
                    show: false,
                    webPreferences: {
                        nodeIntegration: false,
                        contextIsolation: true
                    }
                });

                // Load the HTML content
                await pdfWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(htmlContent)}`);

                // Generate PDF
                const pdfBuffer = await pdfWindow.webContents.printToPDF({
                    format: 'A4',
                    margin: {
                        top: 20,
                        right: 15,
                        bottom: 20,
                        left: 15
                    },
                    printBackground: true,
                    landscape: false
                });

                // Close the window
                pdfWindow.close();

                // Save PDF to file
                await fs.writeFile(filePath, pdfBuffer);

                logger.success(`PDF saved to: ${filePath}`);
                return { success: true };
            } catch (error) {
                logger.error('Error generating PDF:', error);
                return { success: false, error: error.message };
            }
        });
    }

    // Initialize database tables for desktop app
    initializeDesktopTables() {
        try {
            const db = database.getDatabase();

            if (!db) {
                logger.warn('Database not available, skipping desktop table initialization');
                return;
            }

            // Create quiz sessions table if it doesn't exist
            db.exec(`
                CREATE TABLE IF NOT EXISTS quiz_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    question_type TEXT NOT NULL,
                    score_correct INTEGER NOT NULL,
                    score_total INTEGER NOT NULL,
                    duration INTEGER NOT NULL,
                    answers TEXT,
                    questions TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `);

            logger.success('Desktop database tables initialized');
        } catch (error) {
            logger.error('Error initializing desktop tables:', error.message);
            // Don't throw error, just log it
        }
    }
}

module.exports = IPCHandlers;
