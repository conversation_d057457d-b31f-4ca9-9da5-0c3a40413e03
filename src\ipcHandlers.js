const { ipcMain, dialog, shell, Notification } = require('electron');
const path = require('path');
const fs = require('fs');

// Import existing services (adapted for desktop)
const messageHandlers = require('./handlers/messageHandlers');
const apiService = require('./services/apiService');
const fileService = require('./services/fileService');
const database = require('./database/database');
const logger = require('./utils/logger');

class IPCHandlers {
    constructor() {
        this.setupHandlers();
    }

    setupHandlers() {
        // File operations
        ipcMain.handle('select-file', async (event, options = {}) => {
            try {
                const defaultFilters = [
                    { name: 'All Supported', extensions: ['pdf', 'docx', 'doc', 'txt', 'jpg', 'jpeg', 'png', 'bmp', 'tiff', 'tif'] },
                    { name: 'Documents', extensions: ['pdf', 'docx', 'doc', 'txt'] },
                    { name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'bmp', 'tiff', 'tif'] },
                    { name: 'PDF Files', extensions: ['pdf'] },
                    { name: 'Word Documents', extensions: ['docx', 'doc'] },
                    { name: 'Text Files', extensions: ['txt'] },
                    { name: 'All Files', extensions: ['*'] }
                ];

                const result = await dialog.showOpenDialog({
                    properties: ['openFile'],
                    filters: options.filters || defaultFilters,
                    title: options.title || 'Select a file to process'
                });

                if (!result.canceled && result.filePaths.length > 0) {
                    const filePath = result.filePaths[0];
                    logger.info(`File selected: ${filePath}`);
                    return { success: true, filePath: filePath };
                }

                return { success: false, error: 'No file selected' };
            } catch (error) {
                logger.error('Error in select-file:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('process-file', async (event, fileData) => {
            try {
                // Handle different types of file data
                let filePath;

                if (typeof fileData === 'string') {
                    // Direct file path
                    filePath = fileData;
                } else if (fileData && fileData.path) {
                    // File object with path property
                    filePath = fileData.path;
                } else if (fileData && fileData.filePath) {
                    // File object with filePath property
                    filePath = fileData.filePath;
                } else {
                    throw new Error('Invalid file data provided');
                }

                logger.info(`Processing file: ${filePath}`);

                if (!filePath || filePath === 'undefined') {
                    throw new Error('File path is undefined or invalid');
                }

                // Check if file exists
                if (!fs.existsSync(filePath)) {
                    throw new Error(`File not found: ${filePath}`);
                }

                // Use existing file service to extract text
                const result = await fileService.extractTextFromDocument(filePath);

                if (result && result.text) {
                    return {
                        success: true,
                        text: result.text,
                        pageCount: result.pageCount || 1,
                        questionCount: result.questionCount || 15
                    };
                } else {
                    throw new Error('Failed to extract text from file');
                }
            } catch (error) {
                logger.error('Error processing file:', error.message);
                return { success: false, error: error.message };
            }
        });

        // Question generation
        ipcMain.handle('generate-questions', async (event, content, type, count) => {
            try {
                logger.info(`Generating ${count} ${type} questions`);
                
                // Create a mock context object similar to Telegram context
                const mockContext = {
                    from: { id: 'desktop-user' },
                    chat: { id: 'desktop-chat' },
                    reply: (message) => {
                        logger.info('Bot reply:', message);
                        return Promise.resolve();
                    },
                    telegram: {
                        editMessageText: () => Promise.resolve()
                    }
                };

                // Use existing API service to generate questions
                const questions = await apiService.generateQuestionsFromAPI(
                    content, 
                    type, 
                    count, 
                    2, // retries
                    false, // isScanned
                    'desktop-user'
                );

                if (questions && questions.length > 0) {
                    logger.success(`Generated ${questions.length} questions`);
                    return questions;
                } else {
                    throw new Error('No questions were generated');
                }
            } catch (error) {
                logger.error('Error generating questions:', error);
                throw error;
            }
        });

        // Quiz operations
        ipcMain.handle('start-quiz', async (event, questions) => {
            try {
                // Initialize quiz session in database if needed
                const quizSession = {
                    id: Date.now().toString(),
                    questions: questions,
                    startTime: new Date().toISOString(),
                    status: 'active'
                };

                // Store session (you might want to implement this in database)
                logger.info('Quiz session started');
                return { success: true, sessionId: quizSession.id };
            } catch (error) {
                logger.error('Error starting quiz:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('submit-answer', async (event, questionIndex, answer) => {
            try {
                // Process answer submission
                logger.info(`Answer submitted for question ${questionIndex}: ${answer}`);
                return { success: true };
            } catch (error) {
                logger.error('Error submitting answer:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('get-quiz-results', async (event) => {
            try {
                // Get quiz results from database
                return { success: true, results: {} };
            } catch (error) {
                logger.error('Error getting quiz results:', error);
                return { success: false, error: error.message };
            }
        });

        // Database operations
        ipcMain.handle('save-quiz-session', async (event, session) => {
            try {
                // Save quiz session to database
                const sessionData = {
                    timestamp: session.timestamp,
                    question_type: session.questionType,
                    score_correct: session.score.correct,
                    score_total: session.score.total,
                    duration: session.duration,
                    answers: JSON.stringify(session.answers),
                    questions: JSON.stringify(session.questions)
                };

                // Use existing database methods
                const db = database.db();
                const stmt = db.prepare(`
                    INSERT INTO quiz_sessions 
                    (timestamp, question_type, score_correct, score_total, duration, answers, questions)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                `);

                stmt.run(
                    sessionData.timestamp,
                    sessionData.question_type,
                    sessionData.score_correct,
                    sessionData.score_total,
                    sessionData.duration,
                    sessionData.answers,
                    sessionData.questions
                );

                logger.success('Quiz session saved to database');
                return { success: true };
            } catch (error) {
                logger.error('Error saving quiz session:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('get-quiz-history', async (event) => {
            try {
                const db = database.db();
                const stmt = db.prepare(`
                    SELECT * FROM quiz_sessions 
                    ORDER BY timestamp DESC 
                    LIMIT 50
                `);
                
                const sessions = stmt.all();
                
                // Parse JSON fields
                const parsedSessions = sessions.map(session => ({
                    ...session,
                    answers: JSON.parse(session.answers || '[]'),
                    questions: JSON.parse(session.questions || '[]')
                }));

                return { success: true, sessions: parsedSessions };
            } catch (error) {
                logger.error('Error getting quiz history:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('get-statistics', async (event) => {
            try {
                const db = database.db();
                
                // Get basic statistics
                const totalQuizzes = db.prepare('SELECT COUNT(*) as count FROM quiz_sessions').get();
                const avgScore = db.prepare('SELECT AVG(CAST(score_correct AS FLOAT) / score_total * 100) as avg FROM quiz_sessions').get();
                const totalQuestions = db.prepare('SELECT SUM(score_total) as total FROM quiz_sessions').get();
                
                const stats = {
                    totalQuizzes: totalQuizzes.count,
                    averageScore: Math.round(avgScore.avg || 0),
                    totalQuestions: totalQuestions.total || 0,
                    lastQuizDate: null
                };

                // Get last quiz date
                const lastQuiz = db.prepare('SELECT timestamp FROM quiz_sessions ORDER BY timestamp DESC LIMIT 1').get();
                if (lastQuiz) {
                    stats.lastQuizDate = lastQuiz.timestamp;
                }

                return { success: true, statistics: stats };
            } catch (error) {
                logger.error('Error getting statistics:', error);
                return { success: false, error: error.message };
            }
        });

        // Settings
        ipcMain.handle('get-settings', async (event) => {
            try {
                logger.info('Loading settings...');
                // Get settings from config and database
                const config = require('./config/desktop');

                // Try to get settings from database first
                let dbSettings = {};
                try {
                    const db = database.db();
                    if (db) {
                        // Load all settings from database
                        const settingsMap = {
                            'QUESTIONS_PER_PAGE': 'questionsPerPage',
                            'IMAGE_QUESTIONS_COUNT': 'imageQuestionsCount',
                            'SHUFFLE_QUESTIONS': 'shuffleQuestions',
                            'SHUFFLE_OPTIONS': 'shuffleOptions',
                            'SHOW_EXPLANATIONS': 'showExplanations',
                            'NOTIFICATIONS': 'notifications',
                            'AUTO_OPEN_PDF': 'autoOpenPDF',
                            'THEME': 'theme'
                        };

                        for (const [dbKey, settingKey] of Object.entries(settingsMap)) {
                            try {
                                const row = db.prepare('SELECT value FROM config WHERE key = ?').get(dbKey);
                                if (row && row.value !== null) {
                                    // Parse the value based on the setting type
                                    if (settingKey === 'questionsPerPage' || settingKey === 'imageQuestionsCount') {
                                        dbSettings[settingKey] = parseInt(row.value);
                                    } else if (settingKey === 'theme') {
                                        dbSettings[settingKey] = row.value;
                                    } else {
                                        // Boolean settings
                                        dbSettings[settingKey] = row.value === 'true';
                                    }
                                }
                            } catch (queryError) {
                                console.warn(`Failed to load setting ${dbKey}:`, queryError.message);
                            }
                        }
                    }
                } catch (dbError) {
                    logger.warn('Could not load settings from database:', dbError.message);
                }

                const settings = {
                    questionsPerPage: dbSettings.questionsPerPage || config.questionsPerPage || 5,
                    imageQuestionsCount: dbSettings.imageQuestionsCount || config.imageQuestionsCount || 15,
                    shuffleQuestions: dbSettings.shuffleQuestions !== undefined ? dbSettings.shuffleQuestions : (config.shuffleQuestions || false),
                    shuffleOptions: dbSettings.shuffleOptions !== undefined ? dbSettings.shuffleOptions : (config.shuffleOptions || false),
                    showExplanations: dbSettings.showExplanations !== undefined ? dbSettings.showExplanations : (config.showExplanations !== false),
                    notifications: dbSettings.notifications !== undefined ? dbSettings.notifications : (config.notifications !== false),
                    autoOpenPDF: dbSettings.autoOpenPDF !== undefined ? dbSettings.autoOpenPDF : (config.autoOpenPDF !== false),
                    theme: dbSettings.theme || config.theme || 'light',
                    autoSave: config.autoSave !== false,
                    soundEffects: config.soundEffects || false
                };

                logger.info('Settings loaded successfully');
                return { success: true, settings };
            } catch (error) {
                logger.error('Error getting settings:', error.message);
                logger.error('Full error:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('save-settings', async (event, settings) => {
            try {
                logger.info('Attempting to save settings:', settings);

                // Get database instance
                const db = database.db();
                if (!db) {
                    throw new Error('Database not available');
                }

                // Save settings to database
                const updateConfig = db.prepare('INSERT OR REPLACE INTO config (key, value) VALUES (?, ?)');

                // Save each setting to database
                if (settings.questionsPerPage !== undefined) {
                    updateConfig.run('QUESTIONS_PER_PAGE', settings.questionsPerPage.toString());
                }
                if (settings.imageQuestionsCount !== undefined) {
                    updateConfig.run('IMAGE_QUESTIONS_COUNT', settings.imageQuestionsCount.toString());
                }
                if (settings.shuffleQuestions !== undefined) {
                    updateConfig.run('SHUFFLE_QUESTIONS', settings.shuffleQuestions.toString());
                }
                if (settings.shuffleOptions !== undefined) {
                    updateConfig.run('SHUFFLE_OPTIONS', settings.shuffleOptions.toString());
                }
                if (settings.showExplanations !== undefined) {
                    updateConfig.run('SHOW_EXPLANATIONS', settings.showExplanations.toString());
                }
                if (settings.notifications !== undefined) {
                    updateConfig.run('NOTIFICATIONS', settings.notifications.toString());
                }
                if (settings.defaultExportFormat !== undefined) {
                    updateConfig.run('DEFAULT_EXPORT_FORMAT', settings.defaultExportFormat);
                }
                if (settings.theme !== undefined) {
                    updateConfig.run('THEME', settings.theme);
                }
                if (settings.autoOpenPDF !== undefined) {
                    updateConfig.run('AUTO_OPEN_PDF', settings.autoOpenPDF.toString());
                }

                // Update in-memory config for immediate use
                const config = require('./config/desktop');
                if (settings.questionsPerPage !== undefined) {
                    config.questionsPerPage = settings.questionsPerPage;
                    process.env.QUESTIONS_PER_PAGE = settings.questionsPerPage.toString();
                }
                if (settings.imageQuestionsCount !== undefined) {
                    config.imageQuestionsCount = settings.imageQuestionsCount;
                    process.env.IMAGE_QUESTIONS_COUNT = settings.imageQuestionsCount.toString();
                }

                logger.info('Settings saved successfully:', settings);
                return { success: true };
            } catch (error) {
                logger.error('Error saving settings:', error.message);
                return { success: false, error: error.message };
            }
        });

        // Folder selection for downloads
        ipcMain.handle('select-download-folder', async (event) => {
            try {
                const { dialog } = require('electron');
                const result = await dialog.showOpenDialog({
                    properties: ['openDirectory'],
                    title: 'Select Download Folder',
                    buttonLabel: 'Select Folder'
                });

                if (result.canceled) {
                    return { success: true, cancelled: true };
                } else {
                    return { success: true, folderPath: result.filePaths[0] };
                }
            } catch (error) {
                logger.error('Error selecting download folder:', error);
                return { success: false, error: error.message };
            }
        });

        // Export questions to PDF
        ipcMain.handle('export-questions', async (event, questions, format = 'pdf') => {
            try {
                const { dialog } = require('electron');
                const PDFDocument = require('pdfkit');

                // Show save dialog
                const result = await dialog.showSaveDialog({
                    title: 'Export Questions',
                    defaultPath: `questions_${new Date().toISOString().split('T')[0]}.pdf`,
                    filters: [
                        { name: 'PDF Files', extensions: ['pdf'] }
                    ]
                });

                if (result.canceled) {
                    return { success: true, cancelled: true };
                }

                const filePath = result.filePath;

                // Create PDF document
                const doc = new PDFDocument();
                doc.pipe(fs.createWriteStream(filePath));

                // Add title
                doc.fontSize(20).text('Generated Questions', { align: 'center' });
                doc.moveDown(2);

                // Add questions
                questions.forEach((question, index) => {
                    // Question number and text
                    doc.fontSize(14).text(`${index + 1}. ${question.question}`, { continued: false });
                    doc.moveDown(0.5);

                    // Options for MCQ or True/False
                    if (question.options) {
                        // MCQ
                        question.options.forEach((option, optIndex) => {
                            const letter = String.fromCharCode(65 + optIndex);
                            doc.fontSize(12).text(`   ${letter}. ${option}`);
                        });
                    } else {
                        // True/False
                        doc.fontSize(12).text('   T. True');
                        doc.text('   F. False');
                    }

                    doc.moveDown(0.5);

                    // Correct answer
                    doc.fontSize(12).fillColor('green').text(`Correct Answer: ${question.answer}`, { continued: false });
                    doc.fillColor('black');

                    // Explanation if available
                    if (question.explanation) {
                        doc.moveDown(0.3);
                        doc.fontSize(11).fillColor('gray').text(`Explanation: ${question.explanation}`);
                        doc.fillColor('black');
                    }

                    doc.moveDown(1);

                    // Add page break if needed (except for last question)
                    if (index < questions.length - 1 && doc.y > 700) {
                        doc.addPage();
                    }
                });

                // Finalize PDF
                doc.end();

                return { success: true, filePath: filePath };
            } catch (error) {
                logger.error('Error exporting questions:', error);
                return { success: false, error: error.message };
            }
        });

        // Feedback
        ipcMain.handle('submit-feedback', async (event, feedback) => {
            try {
                // Save feedback using existing feedback service
                const feedbackService = require('./services/feedbackService');
                const feedbackData = {
                    userId: 'desktop-user',
                    username: 'Desktop User',
                    rating: feedback.rating,
                    suggestion: feedback.suggestion,
                    quizType: feedback.quizType || 'Unknown',
                    score: feedback.score || 0
                };

                const saved = await feedbackService.saveFeedback(feedbackData);
                
                if (saved) {
                    logger.success('Feedback saved successfully');
                    return { success: true };
                } else {
                    throw new Error('Failed to save feedback');
                }
            } catch (error) {
                logger.error('Error submitting feedback:', error);
                return { success: false, error: error.message };
            }
        });

        // Utility functions
        ipcMain.handle('show-notification', async (event, title, body) => {
            try {
                if (Notification.isSupported()) {
                    new Notification({ title, body }).show();
                }
                return { success: true };
            } catch (error) {
                logger.error('Error showing notification:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('open-external', async (event, url) => {
            try {
                await shell.openExternal(url);
                return { success: true };
            } catch (error) {
                logger.error('Error opening external URL:', error);
                return { success: false, error: error.message };
            }
        });
    }

    // Initialize database tables for desktop app
    initializeDesktopTables() {
        try {
            const db = database.db();

            if (!db) {
                logger.warn('Database not available, skipping desktop table initialization');
                return;
            }

            // Create quiz sessions table if it doesn't exist
            db.exec(`
                CREATE TABLE IF NOT EXISTS quiz_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    question_type TEXT NOT NULL,
                    score_correct INTEGER NOT NULL,
                    score_total INTEGER NOT NULL,
                    duration INTEGER NOT NULL,
                    answers TEXT,
                    questions TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `);

            logger.success('Desktop database tables initialized');
        } catch (error) {
            logger.error('Error initializing desktop tables:', error.message);
            // Don't throw error, just log it
        }
    }
}

module.exports = IPCHandlers;
